const AppError = require('../utils/appError');

/**
 * Middleware to check user authorization based on roles
 * @param {Array} roles - Array of allowed roles
 */
exports.authorize = (roles = []) => {
  return (req, res, next) => {
    if (!req.user) {
      return next(new AppError('Not authenticated. Please log in.', 401));
    }

    if (!roles.includes(req.user.role)) {
      return next(new AppError('You do not have permission to perform this action.', 403));
    }

    next();
  };
};

/**
 * Middleware to check if user has specific permissions
 * @param {Array} permissions - Array of required permissions
 */
exports.requirePermissions = (permissions = []) => {
  return (req, res, next) => {
    if (!req.user) {
      return next(new AppError('Not authenticated. Please log in.', 401));
    }

    // For now, we'll use role-based authorization
    // This can be extended to include specific permissions
    const rolePermissions = {
      'SUPER_ADMIN': ['all'],
      'INSTITUTION_ADMIN': ['manage_users', 'manage_staff', 'manage_students', 'manage_classes', 'view_reports'],
      'SCHOOL_ADMIN': ['manage_users', 'manage_staff', 'manage_students', 'manage_classes', 'view_reports'],
      'ADMIN': ['manage_users', 'manage_staff', 'manage_students', 'view_reports'],
      'TEACHER': ['view_students', 'manage_classes', 'view_reports'],
      'STAFF': ['view_students', 'basic_operations'],
      'STUDENT': ['view_own_data'],
      'PARENT': ['view_children_data']
    };

    const userPermissions = rolePermissions[req.user.role] || [];
    
    if (userPermissions.includes('all')) {
      return next();
    }

    const hasPermission = permissions.some(permission => 
      userPermissions.includes(permission)
    );

    if (!hasPermission) {
      return next(new AppError('You do not have permission to perform this action.', 403));
    }

    next();
  };
};
