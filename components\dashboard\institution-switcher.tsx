"use client"

import { useState } from "react"
import { Check, ChevronsUpDown, PlusCircle } from "lucide-react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from "@/components/ui/command"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { useTenant } from "@/contexts/tenant-context"
import { useRouter } from "next/navigation"

export function InstitutionSwitcher() {
  const [open, setOpen] = useState(false)
  const { currentInstitution, availableInstitutions, switchInstitution, isSuperAdmin } = useTenant()
  const router = useRouter()

  if (!currentInstitution) return null

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          aria-label="Select an institution"
          className="w-[200px] justify-between"
        >
          <div className="flex items-center gap-2 truncate">
            {currentInstitution.logo && (
              <img
                src={currentInstitution.logo || "/placeholder.svg"}
                alt={currentInstitution.name}
                className="h-5 w-5 rounded-full"
              />
            )}
            <span className="truncate">{currentInstitution.name}</span>
          </div>
          <ChevronsUpDown className="ml-auto h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[200px] p-0">
        <Command>
          <CommandList>
            <CommandInput placeholder="Search institution..." />
            <CommandEmpty>No institution found.</CommandEmpty>
            <CommandGroup heading="Your Institutions">
              {availableInstitutions.map((institution) => (
                <CommandItem
                  key={institution.id}
                  onSelect={() => {
                    switchInstitution(institution.id)
                    setOpen(false)
                  }}
                  className="text-sm"
                >
                  <div className="flex items-center gap-2 truncate">
                    {institution.logo && (
                      <img
                        src={institution.logo || "/placeholder.svg"}
                        alt={institution.name}
                        className="h-5 w-5 rounded-full"
                      />
                    )}
                    <span className="truncate">{institution.name}</span>
                  </div>
                  <Check
                    className={cn(
                      "ml-auto h-4 w-4",
                      currentInstitution.id === institution.id ? "opacity-100" : "opacity-0",
                    )}
                  />
                </CommandItem>
              ))}
            </CommandGroup>
            {isSuperAdmin && (
              <>
                <CommandSeparator />
                <CommandGroup>
                  <CommandItem
                    onSelect={() => {
                      setOpen(false)
                      router.push("/super-admin/institutions/new")
                    }}
                  >
                    <PlusCircle className="mr-2 h-4 w-4" />
                    Add Institution
                  </CommandItem>
                </CommandGroup>
              </>
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}
