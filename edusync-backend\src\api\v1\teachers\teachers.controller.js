const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');
const prisma = new PrismaClient();

/**
 * Get all teachers with pagination, filtering, and search
 */
const getTeachers = async (req, res) => {
  try {
   

    const {
      page = 1,
      limit = 10,
      search = '',
      department = '',
      status = '',
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    const skip = (parseInt(page) - 1) * parseInt(limit);
    const take = parseInt(limit);

    // Get user's institution(s) - for multi-tenant support
    let institutionIds = [];

    if (req.user.role === 'SUPER_ADMIN') {
      // Super admin can access all teachers
      institutionIds = null;
    } else {
      // Get user's institutions
      const userInstitutions = await prisma.institutionUser.findMany({
        where: {
          userId: req.user.id,
          isActive: true
        },
        select: { institutionId: true }
      });

      institutionIds = userInstitutions.map(ui => ui.institutionId);

      if (institutionIds.length === 0) {
        return res.status(403).json({
          success: false,
          message: 'Access denied: No institution association found'
        });
      }
    }

    // Build where clause
    const where = {
      AND: []
    };

    // Filter by user's institutions
    if (institutionIds) {
      where.AND.push({
        user: {
          institutions: {
            some: {
              institutionId: { in: institutionIds },
              isActive: true
            }
          }
        }
      });
    }

    // Add search conditions
    if (search) {
      where.AND.push({
        OR: [
          { user: { firstName: { contains: search, mode: 'insensitive' } } },
          { user: { lastName: { contains: search, mode: 'insensitive' } } },
          { user: { email: { contains: search, mode: 'insensitive' } } },
          { employeeId: { contains: search, mode: 'insensitive' } },
          { specialization: { contains: search, mode: 'insensitive' } },
          { qualification: { contains: search, mode: 'insensitive' } }
        ]
      });
    }

    // Add department filter (using specialization as department)
    if (department && department !== 'all') {
      where.AND.push({
        specialization: { contains: department, mode: 'insensitive' }
      });
    }

    // Add status filter
    if (status && status !== 'all') {
      where.AND.push({
        user: {
          isActive: status === 'ACTIVE'
        }
      });
    }

    // If no conditions, remove the AND array
    if (where.AND.length === 0) {
      delete where.AND;
    }

    // Build orderBy clause
    let orderBy = {};
    if (sortBy === 'name') {
      orderBy = { user: { firstName: sortOrder } };
    } else if (sortBy === 'department') {
      orderBy = { specialization: sortOrder };
    } else if (sortBy === 'joiningDate') {
      orderBy = { hireDate: sortOrder };
    } else {
      orderBy = { user: { [sortBy]: sortOrder } };
    }

    // Fetch teachers with relations
    const [teachers, totalCount] = await Promise.all([
      prisma.teacher.findMany({
        where,
        skip,
        take,
        orderBy,
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              phoneNumber: true,
              isActive: true,
              role: true,
              createdAt: true,
              updatedAt: true
            }
          },
          _count: {
            select: {
              assignments: true,
              createdAssignments: true
            }
          }
        }
      }),
      prisma.teacher.count({ where })
    ]);

    const totalPages = Math.ceil(totalCount / take);

    res.json({
      success: true,
      data: teachers,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalRecords: totalCount,
        hasNext: parseInt(page) < totalPages,
        hasPrev: parseInt(page) > 1
      }
    });
  } catch (error) {
    console.error('Error fetching teachers:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch teachers',
      error: error.message
    });
  }
};

/**
 * Get teacher by ID
 */
const getTeacherById = async (req, res) => {
  try {
    const { id } = req.params;

    // Get user's institution(s) - for multi-tenant support
    let institutionIds = [];

    if (req.user.role === 'SUPER_ADMIN') {
      // Super admin can access all teachers
      institutionIds = null;
    } else {
      // Get user's institutions
      const userInstitutions = await prisma.institutionUser.findMany({
        where: {
          userId: req.user.id,
          isActive: true
        },
        select: { institutionId: true }
      });

      institutionIds = userInstitutions.map(ui => ui.institutionId);

      if (institutionIds.length === 0) {
        return res.status(403).json({
          success: false,
          message: 'Access denied: No institution association found'
        });
      }
    }

    const teacher = await prisma.teacher.findFirst({
      where: {
        id,
        ...(institutionIds && {
          user: {
            institutions: {
              some: {
                institutionId: { in: institutionIds },
                isActive: true
              }
            }
          }
        })
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            phoneNumber: true,
            isActive: true,
            role: true,
            isEmailVerified: true,
            createdAt: true,
            updatedAt: true
          }
        },
        assignments: {
          include: {
            subject: {
              select: {
                id: true,
                name: true,
                code: true,
                description: true
              }
            },
            department: {
              select: {
                id: true,
                name: true,
                description: true
              }
            }
          }
        },
        timetableSlots: {
          include: {
            class: {
              select: {
                id: true,
                name: true
              }
            },
            subject: {
              select: {
                id: true,
                name: true,
                code: true
              }
            }
          },
          orderBy: [
            { dayOfWeek: 'asc' },
            { startTime: 'asc' }
          ]
        },
        _count: {
          select: {
            assignments: true,
            timetableSlots: true,
            createdAssignments: true,
            recordedAttendance: true,
            recordedGrades: true
          }
        }
      }
    });

    if (!teacher) {
      return res.status(404).json({
        success: false,
        message: 'Teacher not found'
      });
    }

    res.json({
      success: true,
      data: teacher
    });
  } catch (error) {
    console.error('Error fetching teacher:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch teacher',
      error: error.message
    });
  }
};

/**
 * Create new teacher
 */
const createTeacher = async (req, res) => {
  try {
  

    const {
      firstName,
      lastName,
      email,
      password,
      phoneNumber,
      dateOfBirth,
      gender,
      address,
      emergencyContact,
      employeeId,
      qualification,
      experience,
      specialization,
      salary,
      contractType,
      hireDate,
      institutionId
    } = req.body;

    // Validate required fields
    if (!firstName || !lastName || !email || !password || !institutionId) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: firstName, lastName, email, password, institutionId'
      });
    }

    // Validate password strength
    if (password.length < 8) {
      return res.status(400).json({
        success: false,
        message: 'Password must be at least 8 characters long'
      });
    }

    // Check if user with email already exists
    const existingUser = await prisma.user.findUnique({
      where: { email }
    });

    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: 'User with this email already exists'
      });
    }

    // Check if employeeId is unique within institution
    if (employeeId) {
      const existingEmployee = await prisma.teacher.findFirst({
        where: {
          employeeId,
          user: {
            institutions: {
              some: {
                institutionId: institutionId,
                isActive: true
              }
            }
          }
        }
      });

      if (existingEmployee) {
        return res.status(400).json({
          success: false,
          message: 'Employee ID already exists in this institution'
        });
      }
    }

    // Hash password
    const passwordHash = await bcrypt.hash(password, 12);

    // Create teacher with user in a transaction
    const teacher = await prisma.$transaction(async (tx) => {
      // Create user first
      const user = await tx.user.create({
        data: {
          firstName,
          lastName,
          email,
          passwordHash,
          phoneNumber: phoneNumber || null,
          role: 'TEACHER',
          isActive: true
        }
      });

      // Create institution association
      await tx.institutionUser.create({
        data: {
          userId: user.id,
          institutionId: institutionId,
          role: 'TEACHER',
          isActive: true
        }
      });

      // Create teacher record
      const teacherRecord = await tx.teacher.create({
        data: {
          userId: user.id,
          employeeId: employeeId || null,
          qualification: qualification || null,
          experience: experience ? parseInt(experience) : null,
          specialization: specialization || null,
          salary: salary ? parseFloat(salary) : null,
          contractType: contractType || null,
          hireDate: hireDate ? new Date(hireDate) : new Date()
        },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              phoneNumber: true,
              isActive: true,
              role: true,
              createdAt: true
            }
          }
        }
      });

      return teacherRecord;
    });

    res.status(201).json({
      success: true,
      message: 'Teacher created successfully',
      data: teacher
    });
  } catch (error) {
    console.error('Error creating teacher:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create teacher',
      error: error.message
    });
  }
};

/**
 * Update teacher
 */
const updateTeacher = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      firstName,
      lastName,
      email,
      phoneNumber,
      dateOfBirth,
      gender,
      address,
      emergencyContact,
      employeeId,
      qualification,
      experience,
      specialization,
      salary,
      contractType,
      hireDate,
      isActive,
      institutionId
    } = req.body;

    // Check if teacher exists and belongs to institution
    const existingTeacher = await prisma.teacher.findFirst({
      where: {
        id,
        user: {
          institutionId: institutionId
        }
      },
      include: {
        user: true
      }
    });

    if (!existingTeacher) {
      return res.status(404).json({
        success: false,
        message: 'Teacher not found'
      });
    }

    // Check if email is being changed and if new email exists
    if (email && email !== existingTeacher.user.email) {
      const existingUser = await prisma.user.findUnique({
        where: { email }
      });

      if (existingUser) {
        return res.status(400).json({
          success: false,
          message: 'User with this email already exists'
        });
      }
    }

    // Check if employeeId is being changed and if new employeeId exists
    if (employeeId && employeeId !== existingTeacher.employeeId) {
      const existingEmployee = await prisma.teacher.findFirst({
        where: {
          employeeId,
          user: {
            institutionId
          },
          NOT: {
            id: id
          }
        }
      });

      if (existingEmployee) {
        return res.status(400).json({
          success: false,
          message: 'Employee ID already exists in this institution'
        });
      }
    }

    // Update teacher and user in a transaction
    const updatedTeacher = await prisma.$transaction(async (tx) => {
      // Update user
      await tx.user.update({
        where: { id: existingTeacher.userId },
        data: {
          ...(firstName && { firstName }),
          ...(lastName && { lastName }),
          ...(email && { email }),
          ...(phoneNumber !== undefined && { phoneNumber }),
          ...(isActive !== undefined && { isActive })
        }
      });

      // Update teacher
      const teacherRecord = await tx.teacher.update({
        where: { id },
        data: {
          ...(employeeId !== undefined && { employeeId }),
          ...(qualification !== undefined && { qualification }),
          ...(experience !== undefined && { experience: experience ? parseInt(experience) : null }),
          ...(specialization !== undefined && { specialization }),
          ...(salary !== undefined && { salary: salary ? parseFloat(salary) : null }),
          ...(contractType !== undefined && { contractType }),
          ...(hireDate && { hireDate: new Date(hireDate) })
        },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              phoneNumber: true,
              isActive: true,
              role: true,
              updatedAt: true
            }
          }
        }
      });

      return teacherRecord;
    });

    res.json({
      success: true,
      message: 'Teacher updated successfully',
      data: updatedTeacher
    });
  } catch (error) {
    console.error('Error updating teacher:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update teacher',
      error: error.message
    });
  }
};

/**
 * Delete teacher
 */
const deleteTeacher = async (req, res) => {
  try {
    const { id } = req.params;
    const { institutionId } = req.query;

    // Check if teacher exists and belongs to institution
    const existingTeacher = await prisma.teacher.findFirst({
      where: {
        id,
        user: {
          institutionId: institutionId
        }
      }
    });

    if (!existingTeacher) {
      return res.status(404).json({
        success: false,
        message: 'Teacher not found'
      });
    }

    // Delete teacher (this will cascade delete the user due to foreign key constraints)
    await prisma.teacher.delete({
      where: { id }
    });

    res.json({
      success: true,
      message: 'Teacher deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting teacher:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete teacher',
      error: error.message
    });
  }
};

/**
 * Get teacher assignments
 */
const getTeacherAssignments = async (req, res) => {
  try {
    const { id } = req.params;
    const { institutionId } = req.query;

    const teacher = await prisma.teacher.findFirst({
      where: {
        id,
        user: {
          institutionId: institutionId
        }
      }
    });

    if (!teacher) {
      return res.status(404).json({
        success: false,
        message: 'Teacher not found'
      });
    }

    const assignments = await prisma.teacherAssignment.findMany({
      where: {
        teacherId: id
      },
      include: {
        class: {
          select: {
            id: true,
            name: true,
            level: true,
            capacity: true,
            academicYear: {
              select: {
                year: true,
                name: true,
                startDate: true,
                endDate: true
              }
            }
          }
        },
        subject: {
          select: {
            id: true,
            name: true,
            code: true,
            description: true
          }
        }
      },
      orderBy: [
        { class: { name: 'asc' } },
        { subject: { name: 'asc' } }
      ]
    });

    res.json({
      success: true,
      data: assignments
    });
  } catch (error) {
    console.error('Error fetching teacher assignments:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch teacher assignments',
      error: error.message
    });
  }
};

/**
 * Get teacher schedule/timetable
 */
const getTeacherSchedule = async (req, res) => {
  try {
    const { id } = req.params;
    const { institutionId } = req.query;

    const teacher = await prisma.teacher.findFirst({
      where: {
        id,
        user: {
          institutionId: institutionId
        }
      }
    });

    if (!teacher) {
      return res.status(404).json({
        success: false,
        message: 'Teacher not found'
      });
    }

    const schedule = await prisma.timetableSlot.findMany({
      where: {
        teacherId: id
      },
      include: {
        class: {
          select: {
            id: true,
            name: true,
            level: true
          }
        },
        subject: {
          select: {
            id: true,
            name: true,
            code: true
          }
        }
      },
      orderBy: [
        { dayOfWeek: 'asc' },
        { startTime: 'asc' }
      ]
    });

    res.json({
      success: true,
      data: schedule
    });
  } catch (error) {
    console.error('Error fetching teacher schedule:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch teacher schedule',
      error: error.message
    });
  }
};

/**
 * Get teachers statistics
 */
const getTeachersStats = async (req, res) => {
  try {
    const { institutionId } = req.query;

    const [
      totalTeachers,
      activeTeachers,
      onLeaveTeachers,
      departmentStats,
      qualificationStats
    ] = await Promise.all([
      prisma.teacher.count({
        where: {
          user: {
            institutionId: institutionId
          }
        }
      }),
      prisma.teacher.count({
        where: {
          user: {
            institutionId: institutionId,
            isActive: true
          }
        }
      }),
      prisma.teacher.count({
        where: {
          user: {
            institutionId: institutionId,
            isActive: false
          }
        }
      }),
      prisma.teacher.groupBy({
        by: ['specialization'],
        where: {
          user: {
            institutionId: institutionId
          },
          specialization: {
            not: null
          }
        },
        _count: {
          id: true
        }
      }),
      prisma.teacher.groupBy({
        by: ['qualification'],
        where: {
          user: {
            institutionId: institutionId
          },
          qualification: {
            not: null
          }
        },
        _count: {
          id: true
        }
      })
    ]);

    res.json({
      success: true,
      data: {
        total: totalTeachers,
        active: activeTeachers,
        onLeave: onLeaveTeachers,
        inactive: totalTeachers - activeTeachers - onLeaveTeachers,
        byDepartment: departmentStats.map(stat => ({
          department: stat.specialization,
          count: stat._count.id
        })),
        byQualification: qualificationStats.map(stat => ({
          qualification: stat.qualification,
          count: stat._count.id
        }))
      }
    });
  } catch (error) {
    console.error('Error fetching teachers statistics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch teachers statistics',
      error: error.message
    });
  }
};

module.exports = {
  getTeachers,
  getTeacherById,
  createTeacher,
  updateTeacher,
  deleteTeacher,
  getTeacherAssignments,
  getTeacherSchedule,
  getTeachersStats
};
