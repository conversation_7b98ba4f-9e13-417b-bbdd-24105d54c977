const express = require('express');
const router = express.Router();
const subjectsController = require('./subjects.controller');
const { authenticate } = require('../../../middleware/authenticate');
const { authorize } = require('../../../middleware/authorization');

// Apply authentication middleware to all routes
router.use(authenticate);

// GET /api/v1/subjects/stats - Get subjects statistics
router.get('/stats', authorize(['SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN', 'ADMIN', 'TEACHER', 'STAFF']), subjectsController.getSubjectsStats);

// GET /api/v1/subjects - Get all subjects with filtering and pagination
router.get('/', authorize(['SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN', 'ADMIN', 'TEACHER', 'STAFF']), subjectsController.getSubjects);

// GET /api/v1/subjects/:id - Get subject by ID
router.get('/:id', authorize(['SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN', 'ADMIN', 'TEACHER', 'STAFF']), subjectsController.getSubjectById);

// POST /api/v1/subjects - Create new subject (admin only)
router.post('/', authorize(['SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN', 'ADMIN']), subjectsController.createSubject);

// PUT /api/v1/subjects/:id - Update subject (admin only)
router.put('/:id', authorize(['SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN', 'ADMIN']), subjectsController.updateSubject);

// DELETE /api/v1/subjects/:id - Delete subject (admin only)
router.delete('/:id', authorize(['SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN', 'ADMIN']), subjectsController.deleteSubject);

module.exports = router;