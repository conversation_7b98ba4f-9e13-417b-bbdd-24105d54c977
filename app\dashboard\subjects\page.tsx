"use client"

import { useState, useEffect } from "react"
import {
  PlusCircle,
  Search,
  Filter,
  MoreHorizontal,
  Download,
  Trash2,
  Edit,
  BookOpen,
  Users,
  Clock,
  Loader2,
} from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { subjectService, safeArrayFromResponse } from "@/lib/backend-api"
import { enhancedApiClient } from "@/lib/api-client-enhanced"
import { useToast } from "@/hooks/use-toast"
import { InstitutionProtectedPage } from "@/components/auth/protected-page"

// TypeScript interfaces for Subject
interface Department {
  id: string
  name: string
}

interface Subject {
  id: string
  name: string
  code?: string
  description?: string
  credits?: number
  isCore: boolean
  isActive: boolean
  createdAt: string
  updatedAt: string
  schoolId: string
  departmentId?: string
  department?: Department
  _count?: {
    classes: number
    teacherAssignments: number
  }
}

interface SubjectFormData {
  name: string
  code: string
  description: string
  credits: number
  isCore: boolean
  departmentId: string
}

function SubjectsContent() {
  const [searchTerm, setSearchTerm] = useState("")
  const [isAddSubjectOpen, setIsAddSubjectOpen] = useState(false)
  const [isEditSubjectOpen, setIsEditSubjectOpen] = useState(false)
  const [editingSubject, setEditingSubject] = useState<Subject | null>(null)
  const [subjects, setSubjects] = useState<Subject[]>([])
  const [departments, setDepartments] = useState<Department[]>([])
  const [filteredSubjects, setFilteredSubjects] = useState<Subject[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formData, setFormData] = useState<SubjectFormData>({
    name: "",
    code: "",
    description: "",
    credits: 1,
    isCore: true,
    departmentId: "",
  })
  
  const { toast } = useToast()

  // Fetch departments from API
  const fetchDepartments = async () => {
    try {
      const response = await fetch('/api/departments', {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (response.ok) {
        const result = await response.json()
        if (result.success && result.data) {
          setDepartments(result.data)
        }
      }
    } catch (error) {
      console.error("Error fetching departments:", error)
      // Use fallback departments if API fails
      setDepartments([
        { id: "1", name: "Mathematics" },
        { id: "2", name: "Language Arts" },
        { id: "3", name: "Science" },
        { id: "4", name: "Social Studies" },
        { id: "5", name: "Physical Education" },
        { id: "6", name: "Arts" },
        { id: "7", name: "Foreign Languages" },
        { id: "8", name: "Computer Science" },
        { id: "9", name: "Special Education" },
      ])
    }
  }

  // Fetch subjects from API
  const fetchSubjects = async () => {
    try {
      setIsLoading(true)
      // Try enhanced API client first, fallback to legacy if needed
      let response = await enhancedApiClient.get("/api/subjects")

      // If enhanced client fails, try legacy method
      if (!response.success) {
        console.log("Enhanced API failed, trying legacy method...")
        response = await subjectService.getSubjectsLegacy()
      }

      const subjectsData = safeArrayFromResponse(response)
      setSubjects(subjectsData)
      setFilteredSubjects(subjectsData)

      if (response && !response.success) {
        toast({
          title: "Error",
          description: response.error || "Failed to fetch subjects",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error fetching subjects:", error)
      // Arrays are already set to safe values above
      toast({
        title: "Error",
        description: "Failed to fetch subjects",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Load subjects and departments on component mount
  useEffect(() => {
    fetchSubjects()
    fetchDepartments()
  }, [])

  // Update filtered subjects when search term or subjects change
  useEffect(() => {
    if (searchTerm === "") {
      setFilteredSubjects(subjects)
      return
    }

    const filtered = subjects.filter(
      (subject) =>
        subject.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (subject.code && subject.code.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (subject.department?.name && subject.department.name.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (subject.description && subject.description.toLowerCase().includes(searchTerm.toLowerCase()))
    )
    setFilteredSubjects(filtered)
  }, [searchTerm, subjects])

  // Handle form input changes
  const handleInputChange = (field: keyof SubjectFormData, value: string | number | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }))
  }

  // Handle create subject
  const handleCreateSubject = async () => {
    if (!formData.name.trim()) {
      toast({
        title: "Validation Error",
        description: "Subject name is required",
        variant: "destructive",
      })
      return
    }

    try {
      setIsSubmitting(true)
      const response = await subjectService.createSubject(formData)
      
      if (response.success) {
        toast({
          title: "Success",
          description: "Subject created successfully",
        })
        setIsAddSubjectOpen(false)
        setFormData({
          name: "",
          code: "",
          description: "",
          credits: 1,
          isCore: true,
          departmentId: "",
        })
        await fetchSubjects() // Refresh the list
      } else {
        toast({
          title: "Error",
          description: response.error || "Failed to create subject",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error creating subject:", error)
      toast({
        title: "Error",
        description: "Failed to create subject",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle edit subject
  const handleEditSubject = (subject: Subject) => {
    setEditingSubject(subject)
    setFormData({
      name: subject.name,
      code: subject.code || "",
      description: subject.description || "",
      credits: subject.credits || 1,
      isCore: subject.isCore,
      departmentId: subject.departmentId || "",
    })
    setIsEditSubjectOpen(true)
  }

  // Handle update subject
  const handleUpdateSubject = async () => {
    if (!formData.name.trim()) {
      toast({
        title: "Validation Error",
        description: "Subject name is required",
        variant: "destructive",
      })
      return
    }

    if (!editingSubject) return

    try {
      setIsSubmitting(true)
      const response = await fetch(`/api/subjects/${editingSubject.id}`, {
        method: 'PUT',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      const result = await response.json()

      if (result.success) {
        toast({
          title: "Success",
          description: "Subject updated successfully",
        })
        setIsEditSubjectOpen(false)
        setEditingSubject(null)
        setFormData({
          name: "",
          code: "",
          description: "",
          credits: 1,
          isCore: true,
          departmentId: "",
        })
        await fetchSubjects() // Refresh the list
      } else {
        toast({
          title: "Error",
          description: result.message || "Failed to update subject",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error updating subject:", error)
      toast({
        title: "Error",
        description: "Failed to update subject",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle delete subject
  const handleDeleteSubject = async (id: string) => {
    if (!confirm("Are you sure you want to delete this subject? This action cannot be undone.")) {
      return
    }

    try {
      const response = await subjectService.deleteSubject(id)
      
      if (response.success) {
        toast({
          title: "Success",
          description: "Subject deleted successfully",
        })
        await fetchSubjects() // Refresh the list
      } else {
        toast({
          title: "Error",
          description: response.error || "Failed to delete subject",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error deleting subject:", error)
      toast({
        title: "Error",
        description: "Failed to delete subject",
        variant: "destructive",
      })
    }
  }

  // Filter subjects based on search term
  const handleSearch = (term: string) => {
    setSearchTerm(term)
  }

  return (
    <div className="space-y-6 p-6">
      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Subjects Management</h2>
          <p className="text-muted-foreground">Manage subjects, curricula, and assign teachers</p>
        </div>
        <div className="flex items-center space-x-2">
          <Dialog open={isAddSubjectOpen} onOpenChange={setIsAddSubjectOpen}>
            <DialogTrigger asChild>
              <Button>
                <PlusCircle className="mr-2 h-4 w-4" />
                Add Subject
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[525px]">
              <DialogHeader>
                <DialogTitle>Add New Subject</DialogTitle>
                <DialogDescription>
                  Enter the details of the new subject. Click save when you're done.
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="subject-name" className="text-right">
                    Subject Name
                  </Label>
                  <Input 
                    id="subject-name" 
                    className="col-span-3"
                    value={formData.name}
                    onChange={(e) => handleInputChange("name", e.target.value)}
                    placeholder="Enter subject name"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="subject-code" className="text-right">
                    Subject Code
                  </Label>
                  <Input 
                    id="subject-code" 
                    className="col-span-3"
                    value={formData.code}
                    onChange={(e) => handleInputChange("code", e.target.value)}
                    placeholder="e.g., MATH101"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="credits" className="text-right">
                    Credits
                  </Label>
                  <Input 
                    id="credits" 
                    type="number"
                    min="1"
                    max="10"
                    className="col-span-3"
                    value={formData.credits}
                    onChange={(e) => handleInputChange("credits", parseInt(e.target.value) || 1)}
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="department" className="text-right">
                    Department
                  </Label>
                  <Select value={formData.departmentId} onValueChange={(value) => handleInputChange("departmentId", value)}>
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder="Select department" />
                    </SelectTrigger>
                    <SelectContent>
                      {departments.map((dept) => (
                        <SelectItem key={dept.id} value={dept.id}>
                          {dept.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-4 items-start gap-4">
                  <Label htmlFor="description" className="text-right pt-2">
                    Description
                  </Label>
                  <Textarea 
                    id="description" 
                    className="col-span-3" 
                    rows={3}
                    value={formData.description}
                    onChange={(e) => handleInputChange("description", e.target.value)}
                    placeholder="Enter subject description"
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddSubjectOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleCreateSubject} disabled={isSubmitting}>
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    "Create Subject"
                  )}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          {/* Edit Subject Dialog */}
          <Dialog open={isEditSubjectOpen} onOpenChange={setIsEditSubjectOpen}>
            <DialogContent className="sm:max-w-[525px]">
              <DialogHeader>
                <DialogTitle>Edit Subject</DialogTitle>
                <DialogDescription>
                  Update the subject details. Click save when you're done.
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="edit-name" className="text-right">
                    Name
                  </Label>
                  <Input
                    id="edit-name"
                    className="col-span-3"
                    value={formData.name}
                    onChange={(e) => handleInputChange("name", e.target.value)}
                    placeholder="Enter subject name"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="edit-code" className="text-right">
                    Code
                  </Label>
                  <Input
                    id="edit-code"
                    className="col-span-3"
                    value={formData.code}
                    onChange={(e) => handleInputChange("code", e.target.value)}
                    placeholder="Enter subject code (optional)"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="edit-credits" className="text-right">
                    Credits
                  </Label>
                  <Input
                    id="edit-credits"
                    type="number"
                    min="1"
                    max="10"
                    className="col-span-3"
                    value={formData.credits}
                    onChange={(e) => handleInputChange("credits", parseInt(e.target.value) || 1)}
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="edit-department" className="text-right">
                    Department
                  </Label>
                  <Select value={formData.departmentId} onValueChange={(value) => handleInputChange("departmentId", value)}>
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder="Select department" />
                    </SelectTrigger>
                    <SelectContent>
                      {departments.map((dept) => (
                        <SelectItem key={dept.id} value={dept.id}>
                          {dept.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-4 items-start gap-4">
                  <Label htmlFor="edit-description" className="text-right pt-2">
                    Description
                  </Label>
                  <Textarea
                    id="edit-description"
                    className="col-span-3"
                    value={formData.description}
                    onChange={(e) => handleInputChange("description", e.target.value)}
                    placeholder="Enter subject description (optional)"
                    rows={3}
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="edit-isCore" className="text-right">
                    Core Subject
                  </Label>
                  <div className="col-span-3">
                    <Select
                      value={formData.isCore ? "true" : "false"}
                      onValueChange={(value) => handleInputChange("isCore", value === "true")}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="true">Yes</SelectItem>
                        <SelectItem value="false">No</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsEditSubjectOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleUpdateSubject} disabled={isSubmitting}>
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Updating...
                    </>
                  ) : (
                    "Update Subject"
                  )}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle>Subject Directory</CardTitle>
          <CardDescription>View and manage all subjects in your institution</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0 mb-4">
            <div className="relative w-full md:w-96">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search subjects..."
                className="w-full pl-8"
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
              />
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm">
                <Filter className="mr-2 h-4 w-4" />
                Filter
              </Button>
            </div>
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Subject Name</TableHead>
                  <TableHead>Code</TableHead>
                  <TableHead>Department</TableHead>
                  <TableHead>Credits</TableHead>
                  <TableHead>Teachers</TableHead>
                  <TableHead>Classes</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8">
                      <div className="flex items-center justify-center">
                        <Loader2 className="h-6 w-6 animate-spin mr-2" />
                        Loading subjects...
                      </div>
                    </TableCell>
                  </TableRow>
                ) : filteredSubjects.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8">
                      {searchTerm ? "No subjects found matching your search." : "No subjects found. Create your first subject to get started."}
                    </TableCell>
                  </TableRow>
                ) : (
                  (Array.isArray(filteredSubjects) ? filteredSubjects : []).map((subject) => (
                    <TableRow key={subject.id}>
                      <TableCell className="font-medium">{subject.name}</TableCell>
                      <TableCell>{subject.code || "-"}</TableCell>
                      <TableCell>{subject.department?.name || "-"}</TableCell>
                      <TableCell>{subject.credits || "-"}</TableCell>
                      <TableCell>{subject._count?.teacherAssignments || 0}</TableCell>
                      <TableCell>{subject._count?.classes || 0}</TableCell>
                      <TableCell>
                        <Badge variant={subject.isActive ? "default" : "secondary"}>
                          {subject.isActive ? "Active" : "Inactive"}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                              <span className="sr-only">Open menu</span>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleEditSubject(subject)}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <BookOpen className="mr-2 h-4 w-4" />
                              View Curriculum
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Users className="mr-2 h-4 w-4" />
                              Assign Teachers
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Clock className="mr-2 h-4 w-4" />
                              Set Schedule
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              className="text-destructive"
                              onClick={() => handleDeleteSubject(subject.id)}
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default function SubjectsPage() {
  return (
    <InstitutionProtectedPage>
      <SubjectsContent />
    </InstitutionProtectedPage>
  )
}
