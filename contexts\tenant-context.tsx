"use client"

import type React from "react"
import { createContext, useContext, useState, useEffect } from "react"
import { useRouter, usePathname } from "next/navigation"
import { useAuth } from "./auth-context"

export type Institution = {
  id: string
  name: string
  logo?: string
  domain?: string
  subdomain: string
  isActive: boolean
  address?: string
  city?: string
  state?: string
  country?: string
  phoneNumber?: string
  email?: string
  type?: string
}

export type User = {
  id: string
  firstName: string
  lastName: string
  email: string
  role: "SUPER_ADMIN" | "INSTITUTION_ADMIN" | "SCHOOL_ADMIN" | "ADMIN" | "TEACHER" | "STUDENT" | "PARENT" | "STAFF"
  institutionId?: string
  schoolId?: string
}

type TenantContextType = {
  currentInstitution: Institution | null
  setCurrentInstitution: (institution: Institution | null) => void
  availableInstitutions: Institution[]
  currentUser: User | null
  currentSchool: any | null
  isLoading: boolean
  switchInstitution: (institutionId: string) => void
  isSuperAdmin: boolean
  isInstitutionAdmin: boolean
  refreshUserData: () => Promise<void>
}

const TenantContext = createContext<TenantContextType | undefined>(undefined)

export const TenantProvider = ({ children }: { children: React.ReactNode }) => {
  const router = useRouter()
  const pathname = usePathname()
  const { user, isLoading: authLoading, isAuthenticated } = useAuth()

  const [currentInstitution, setCurrentInstitution] = useState<Institution | null>(null)
  const [availableInstitutions, setAvailableInstitutions] = useState<Institution[]>([])
  const [currentUser, setCurrentUser] = useState<User | null>(null)
  const [currentSchool, setCurrentSchool] = useState<any | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    if (!authLoading) {
      if (isAuthenticated && user) {
        initializeTenant()
      } else {
        setIsLoading(false)
      }
    }
  }, [authLoading, isAuthenticated, user])

  const initializeTenant = async () => {
    setIsLoading(true)
    try {
      // Set current user from auth context
      const userData: User = {
        id: user!.id,
        firstName: user!.firstName,
        lastName: user!.lastName,
        email: user!.email,
        role: user!.role as any,
      }
      setCurrentUser(userData)

      // Get user's institution associations from the session
      await fetchUserInstitutions()

    } catch (error) {
      console.error("Failed to initialize tenant context:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const fetchUserInstitutions = async () => {
    try {
     
      // Get user session data which includes institution associations
      const response = await fetch("/api/auth/session", {
        credentials: "include"
      })

      if (!response.ok) {
        throw new Error("Failed to fetch user session")
      }

      const sessionData = await response.json()
      const userInstitutions = sessionData.user?.institutions || []

      

      if (userInstitutions.length === 0) {
    
        setAvailableInstitutions([])
        setCurrentInstitution(null)
        return
      }

      // Fetch detailed institution data for each association
      const institutionPromises = userInstitutions.map(async (userInst: any) => {
        const instResponse = await fetch(`/api/institutions/${userInst.institution.id}`, {
          credentials: 'include',
        })

        if (instResponse.ok) {
          const instData = await instResponse.json()
          return instData.data?.institution || instData.institution || instData
        }
        return null
      })

      const institutions = (await Promise.all(institutionPromises)).filter(Boolean)
     

      setAvailableInstitutions(institutions)

      // Set current institution from localStorage or default to first
      const storedInstitutionId = localStorage.getItem("currentInstitutionId")
      const currentInst = storedInstitutionId
        ? institutions.find((inst: Institution) => inst.id === storedInstitutionId)
        : institutions[0]

      if (currentInst) {
   
        setCurrentInstitution(currentInst)
        localStorage.setItem("currentInstitutionId", currentInst.id)
      } else {
      
        setCurrentInstitution(null)
      }

    } catch (error) {
      console.error("Failed to fetch user institutions:", error)
      setAvailableInstitutions([])
      setCurrentInstitution(null)
    }
  }

  const fetchAllInstitutions = async () => {
    try {
      const response = await fetch("/api/institutions", {
        credentials: "include"
      })
      
      if (response.ok) {
        const data = await response.json()
        setAvailableInstitutions(data.institutions || [])
        
        // Set current institution from localStorage or default to first
        const storedInstitutionId = localStorage.getItem("currentInstitutionId")
        const institution = storedInstitutionId 
          ? data.institutions.find((inst: Institution) => inst.id === storedInstitutionId)
          : data.institutions[0]
          
        if (institution) {
          setCurrentInstitution(institution)
          localStorage.setItem("currentInstitutionId", institution.id)
        }
      }
    } catch (error) {
      console.error("Failed to fetch institutions:", error)
    }
  }

  const fetchUserInstitution = async (institutionId: string) => {
    try {
      const response = await fetch(`/api/institutions/${institutionId}`, {
        credentials: "include"
      })
      
      if (response.ok) {
        const data = await response.json()
        const institution = data.institution
        setAvailableInstitutions([institution])
        setCurrentInstitution(institution)
        localStorage.setItem("currentInstitutionId", institution.id)
      }
    } catch (error) {
      console.error("Failed to fetch user institution:", error)
    }
  }

  const switchInstitution = async (institutionId: string) => {
    const institution = availableInstitutions.find((inst) => inst.id === institutionId)
    if (institution) {
      setCurrentInstitution(institution)
      localStorage.setItem("currentInstitutionId", institutionId)

      // Redirect appropriately
      if (pathname.includes("/super-admin")) {
        router.push(`/super-admin/institutions/${institutionId}`)
      } else {
        router.push("/dashboard")
      }
    }
  }

  const refreshUserData = async () => {
    await initializeTenant()
  }

  const isSuperAdmin = currentUser?.role === "SUPER_ADMIN"
  const isInstitutionAdmin = currentUser?.role === "INSTITUTION_ADMIN" || currentUser?.role === "ADMIN"

  const value = {
    currentInstitution,
    setCurrentInstitution,
    availableInstitutions,
    currentUser,
    currentSchool,
    isLoading: authLoading || isLoading,
    switchInstitution,
    isSuperAdmin,
    isInstitutionAdmin,
    refreshUserData,
  }

  // Only show loading screen for authenticated users during tenant initialization
  if (authLoading || (isAuthenticated && isLoading)) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Initializing...</p>
        </div>
      </div>
    )
  }

  return <TenantContext.Provider value={value}>{children}</TenantContext.Provider>
}

export const useTenant = () => {
  const context = useContext(TenantContext)
  if (context === undefined) {
    throw new Error("useTenant must be used within a TenantProvider")
  }
  return context
}
