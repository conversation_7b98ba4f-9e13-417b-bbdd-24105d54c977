import { NextRequest, NextResponse } from "next/server"
import { getValidAuthToken } from "@/lib/auth-utils"

const BACKEND_URL = process.env.BACKEND_URL || "http://localhost:4000"

export async function GET(request: NextRequest) {
  try {
    console.log("🔍 Subjects API: Getting auth token...")
    const token = await getValidAuthToken(request)
    console.log("🔍 Subjects API: Token received:", token ? "✅ Valid" : "❌ Null")

    if (!token) {
      console.log("❌ Subjects API: No valid token, returning 401")
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const queryString = searchParams.toString()

    console.log("🔍 Subjects API: Making request to backend...")
    const response = await fetch(`${BACKEND_URL}/api/v1/subjects?${queryString}`, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    })

    console.log("🔍 Subjects API: Backend response status:", response.status)
    const data = await response.json()
    console.log("🔍 Subjects API: Backend response data:", data)

    if (!response.ok) {
      console.log("❌ Subjects API: Backend returned error:", response.status, data)
      return NextResponse.json(data, { status: response.status })
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error("Subjects API error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const token = await getValidAuthToken(request)

    if (!token) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()

    const response = await fetch(`${BACKEND_URL}/api/v1/subjects`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(body),
    })

    const data = await response.json()

    if (!response.ok) {
      return NextResponse.json(data, { status: response.status })
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error("Create subject API error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
