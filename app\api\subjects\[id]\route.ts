import { NextResponse } from "next/server"

export async function GET(request: Request, { params }: { params: { id: string } }) {
  try {
    // Forward the request to our Flask backend
    const response = await fetch(`http://localhost:4000/api/v1/subjects/${params.id}`, {
      headers: {
        Cookie: request.headers.get("cookie") || "",
      },
    })

    if (!response.ok) {
      return NextResponse.json({ error: "Failed to fetch subject" }, { status: response.status })
    }

    const data = await response.json()
    return NextResponse.json(data)
  } catch (error) {
    console.error("Subject API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function PUT(request: Request, { params }: { params: { id: string } }) {
  try {
    const body = await request.json()

    // Forward the request to our Flask backend
    const response = await fetch(`http://localhost:4000/api/v1/subjects/${params.id}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Cookie: request.headers.get("cookie") || "",
      },
      body: JSON.stringify(body),
    })

    const data = await response.json()

    if (!response.ok) {
      return NextResponse.json({ error: data.message || "Failed to update subject" }, { status: response.status })
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error("Update subject API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function DELETE(request: Request, { params }: { params: { id: string } }) {
  try {
    // Forward the request to our Flask backend
    const response = await fetch(`http://localhost:4000/api/v1/subjects/${params.id}`, {
      method: "DELETE",
      headers: {
        Cookie: request.headers.get("cookie") || "",
      },
    })

    const data = await response.json()

    if (!response.ok) {
      return NextResponse.json({ error: data.message || "Failed to delete subject" }, { status: response.status })
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error("Delete subject API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
