// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

enum UserRole {
  SUPER_ADMIN
  INSTITUTION_ADMIN
  SCHOOL_ADMIN
  ADMIN
  TEACHER
  STUDENT
  PARENT
  STAFF
  LIBRARIAN
  ACCOUNTANT
  PRINCIPAL
  VICE_PRINCIPAL
}

enum Gender {
  MALE
  FEMALE
  OTHER
}

enum ContractType {
  FULL_TIME
  PART_TIME
  CONTRACT
  TEMPORARY
}

enum StudentStatus {
  ACTIVE
  INACTIVE
  GRADUATED
  TRANSFERRED
  SUSPENDED
  EXPELLED
}

enum SubscriptionStatus {
  ACTIVE
  INACTIVE
  TRIAL
  EXPIRED
}

enum AttendanceStatus {
  PRESENT
  ABSENT
  LATE
  EXCUSED
}

enum ExamStatus {
  SCHEDULED
  UPCOMING
  ONGOING
  COMPLETED
  CANCELLED
}

enum FeeStatus {
  PENDING
  PAID
  OVERDUE
  CANCELLED
}

enum GradeScale {
  LETTER
  PERCENTAGE
  GPA
  POINTS
}

enum AcademicTermStatus {
  UPCOMING
  CURRENT
  COMPLETED
}

enum NotificationStatus {
  UNREAD
  READ
  ARCHIVED
}

enum NotificationType {
  GENERAL
  ACADEMIC
  ATTENDANCE
  FEE
  EXAM
  GRADE
  DISCIPLINARY
  EVENT
}

// Event types for academic calendar
enum EventType {
  ACADEMIC
  MEETING
  EXAM
  EVENT
  HOLIDAY
  CONFERENCE
  WORKSHOP
  CELEBRATION
  SPORTS
  CULTURAL
}

// Report types for the reporting system
enum ReportType {
  ATTENDANCE
  FEE_COLLECTION
  EXAM_RESULTS
  TEACHER_PERFORMANCE
  CLASS_PERFORMANCE
  STUDENT_PROGRESS
  ACADEMIC_SUMMARY
  DISCIPLINARY
  FINANCIAL
}

// Notification delivery methods
enum NotificationDeliveryMethod {
  EMAIL
  SMS
  IN_APP
  PUSH
}

enum SchoolType {
  PRIMARY
  SECONDARY
  TERTIARY
  KINDERGARTEN
  VOCATIONAL
  SPECIAL_NEEDS
}

// Base User model - contains common authentication and profile data
model User {
  id                       String    @id @default(uuid())
  email                    String    @unique
  passwordHash             String
  firstName                String
  lastName                 String
  role                     UserRole
  phoneNumber              String?
  profileImageUrl          String?
  isEmailVerified          Boolean   @default(false)
  isPhoneVerified          Boolean   @default(false)
  isActive                 Boolean   @default(true)
  createdAt                DateTime  @default(now())
  updatedAt                DateTime  @updatedAt
  lastLoginAt              DateTime?
  passwordResetToken       String?
  passwordResetExpires     DateTime?
  emailVerificationToken   String?
  emailVerificationExpires DateTime?

  // MFA fields
  mfaEnabled  Boolean @default(false)
  mfaSecret   String?
  backupCodes String? // JSON array of backup codes

  // Base relations
  institutions      InstitutionUser[]
  schools           SchoolUser[]
  refreshTokens     RefreshToken[]
  auditLogs         AuditLog[]
  fileUploads       FileUpload[]
  notifications     Notification[]    @relation("NotificationRecipient")
  sentNotifications Notification[]    @relation("NotificationSender")
  supportTickets    SupportTicket[]   @relation("SupportTickets")
  assignedTickets   SupportTicket[]   @relation("AssignedTickets")
  supportResponses  SupportResponse[] @relation("SupportResponses")

  // Role-specific profile relations
  studentProfile Student?
  teacherProfile Teacher?
  staffProfile   Staff?
  parentProfile  Parent?
}

// Student-specific data and relations
model Student {
  id     String @id @default(uuid())
  userId String @unique
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Student-specific fields
  admissionNumber   String?       @unique
  rollNumber        String?
  dateOfBirth       DateTime?
  gender            Gender?
  address           String?
  city              String?
  state             String?
  country           String?
  postalCode        String?
  nationality       String?
  bloodGroup        String?
  medicalConditions String?       @db.Text
  emergencyContact  String?
  joinDate          DateTime?
  graduationDate    DateTime?
  studentStatus     StudentStatus @default(ACTIVE)

  // Student-specific relations
  enrollments         StudentEnrollment[]
  attendanceRecords   AttendanceRecord[]
  gradeRecords        GradeRecord[]
  examResults         ExamResult[]
  feePayments         FeePayment[]
  disciplinaryRecords DisciplinaryRecord[]
  parents             StudentParent[]
  libraryBorrows      LibraryBorrowRecord[]
}

// Teacher-specific data and relations
model Teacher {
  id     String @id @default(uuid())
  userId String @unique
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Teacher-specific fields
  employeeId     String?       @unique
  qualification  String?
  experience     Int? // years of experience
  specialization String?
  salary         Decimal?      @db.Decimal(10, 2)
  contractType   ContractType?
  hireDate       DateTime?

  // Teacher-specific relations
  assignments          TeacherAssignment[]
  timetableSlots       TimetableSlot[]
  createdAssignments   Assignment[]
  recordedAttendance   AttendanceRecord[]    @relation("AttendanceRecorder")
  recordedGrades       GradeRecord[]         @relation("GradeRecorder")
  createdEvents        Event[]               @relation("EventCreator")
  reportedDisciplinary DisciplinaryRecord[]  @relation("DisciplinaryReporter")
  libraryIssuer        LibraryBorrowRecord[] @relation("LibraryIssuer")
  generatedReports     Report[]              @relation("GeneratedReports")
}

// Staff-specific data and relations (non-teaching staff)
model Staff {
  id     String @id @default(uuid())
  userId String @unique
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Staff-specific fields
  employeeId    String?       @unique
  qualification String?
  experience    Int? // years of experience
  department    String?
  jobTitle      String?
  salary        Decimal?      @db.Decimal(10, 2)
  contractType  ContractType?
  hireDate      DateTime?

  // Staff-specific relations
  recordedPayments FeePayment[]          @relation("PaymentRecorder")
  libraryIssuer    LibraryBorrowRecord[] @relation("StaffLibraryIssuer")
  generatedReports Report[]              @relation("GeneratedReports")
}

// Parent-specific data and relations
model Parent {
  id     String @id @default(uuid())
  userId String @unique
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Parent-specific fields
  occupation       String?
  workAddress      String?
  workPhone        String?
  emergencyContact String?

  // Parent-specific relations
  children StudentParent[]
}

model Institution {
  id                  String             @id @default(uuid())
  name                String
  domain              String?            @unique
  logo                String?
  primaryColor        String?
  secondaryColor      String?
  isActive            Boolean            @default(true)
  subscriptionStatus  SubscriptionStatus @default(TRIAL)
  subscriptionEndDate DateTime?
  createdAt           DateTime           @default(now())
  updatedAt           DateTime           @updatedAt

  // Additional registration fields
  address             String?
  city                String?
  state               String?
  country             String?
  postalCode          String?
  phoneNumber         String?
  email               String?
  website             String?
  registrationDate    DateTime @default(now())
  verificationStatus  Boolean  @default(false)
  studentCount        Int?
  teacherCount        Int?
  referralSource      String?
  specialRequirements String?  @db.Text

  // Relations
  users               InstitutionUser[]
  schools             School[]
  fileUploads         FileUpload[]
  academicYears       AcademicYear[]
  subscriptionHistory SubscriptionHistory[]
  subscription        Subscription?

  // Additional relations
  supportTickets SupportTicket[] @relation("InstitutionSupport")
}

model School {
  id            String     @id @default(uuid())
  name          String
  type          SchoolType @default(TERTIARY)
  institutionId String
  address       String?
  city          String?
  state         String?
  country       String?
  postalCode    String?
  phoneNumber   String?
  email         String?
  website       String?
  isActive      Boolean    @default(true)
  createdAt     DateTime   @default(now())
  updatedAt     DateTime   @updatedAt

  // Relations
  institution Institution  @relation(fields: [institutionId], references: [id], onDelete: Cascade)
  users       SchoolUser[]
  fileUploads FileUpload[]

  // Academic structure
  academicYears AcademicYear[]
  classes       Class[]
  subjects      Subject[]
  departments   Department[]

  // Academic activities
  studentEnrollments StudentEnrollment[]
  attendanceRecords  AttendanceRecord[]
  exams              Exam[]
  feeStructures      FeeStructure[]
  events             Event[]
  notifications      Notification[]

  // Additional relations for reporting
  reports      Report[]
  libraryBooks LibraryBook[] @relation("LibraryBooks")
}

model InstitutionUser {
  id            String   @id @default(uuid())
  userId        String
  institutionId String
  role          UserRole
  isActive      Boolean  @default(true)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  user        User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  institution Institution @relation(fields: [institutionId], references: [id], onDelete: Cascade)

  @@unique([userId, institutionId])
}

model SchoolUser {
  id        String   @id @default(uuid())
  userId    String
  schoolId  String
  role      UserRole
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  school School @relation(fields: [schoolId], references: [id], onDelete: Cascade)

  @@unique([userId, schoolId])
}

model RefreshToken {
  id        String   @id @default(uuid())
  token     String   @unique
  userId    String
  expiresAt DateTime
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  isRevoked Boolean  @default(false)

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model AuditLog {
  id           String   @id @default(uuid())
  userId       String
  action       String
  details      String?  @db.Text
  ipAddress    String?
  userAgent    String?
  resourceType String?
  resourceId   String?
  createdAt    DateTime @default(now())

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

// Model for tracking file uploads
model FileUpload {
  id               String   @id @default(uuid())
  filename         String
  originalFilename String
  mimeType         String
  size             Int
  path             String
  url              String?
  isPublic         Boolean  @default(false)
  uploadedAt       DateTime @default(now())

  // Who uploaded the file
  userId String
  user   User   @relation(fields: [userId], references: [id])

  // Optional relations to link file to different entities
  institutionId String?
  institution   Institution? @relation(fields: [institutionId], references: [id], onDelete: Cascade)

  schoolId String?
  school   School? @relation(fields: [schoolId], references: [id], onDelete: Cascade)

  // Metadata
  metadata String? @db.Text // JSON with any additional metadata
}

// Academic Year Management
model AcademicYear {
  id        String   @id @default(uuid())
  name      String // e.g., "2023-2024"
  startDate DateTime
  endDate   DateTime
  isActive  Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  institutionId String?
  institution   Institution? @relation(fields: [institutionId], references: [id], onDelete: Cascade)

  schoolId String
  school   School @relation(fields: [schoolId], references: [id], onDelete: Cascade)

  terms              AcademicTerm[]
  classes            Class[]
  studentEnrollments StudentEnrollment[]
  exams              Exam[]
  attendanceRecords  AttendanceRecord[]
  gradeRecords       GradeRecord[]
  feeStructures      FeeStructure[]
  events             Event[]

  @@unique([schoolId, name])
}

// Academic Terms/Semesters
model AcademicTerm {
  id        String             @id @default(uuid())
  name      String // e.g., "First Term", "Fall Semester"
  startDate DateTime
  endDate   DateTime
  status    AcademicTermStatus @default(UPCOMING)
  createdAt DateTime           @default(now())
  updatedAt DateTime           @updatedAt

  // Relations
  academicYearId String
  academicYear   AcademicYear @relation(fields: [academicYearId], references: [id], onDelete: Cascade)

  events       Event[]
  gradeRecords GradeRecord[]
  exams        Exam[]

  @@unique([academicYearId, name])
}

// Department Management
model Department {
  id                 String   @id @default(uuid())
  name               String
  description        String?
  headOfDepartmentId String?
  isActive           Boolean  @default(true)
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt

  // Relations
  schoolId String
  school   School @relation(fields: [schoolId], references: [id], onDelete: Cascade)

  subjects Subject[]
  teachers TeacherAssignment[]

  @@unique([schoolId, name])
}

// Class Management
model Class {
  id             String   @id @default(uuid())
  name           String // e.g., "Grade 5A", "Form 1B"
  gradeLevel     String // e.g., "5", "Form 1"
  section        String? // e.g., "A", "B"
  capacity       Int?
  classTeacherId String?
  roomNumber     String?
  isActive       Boolean  @default(true)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relations
  schoolId String
  school   School @relation(fields: [schoolId], references: [id], onDelete: Cascade)

  academicYearId String
  academicYear   AcademicYear @relation(fields: [academicYearId], references: [id], onDelete: Cascade)

  studentEnrollments StudentEnrollment[]
  subjects           ClassSubject[]
  attendanceRecords  AttendanceRecord[]
  timetableSlots     TimetableSlot[]
  exams              ExamClass[]

  @@unique([schoolId, academicYearId, name])
}

// Subject Management
model Subject {
  id          String   @id @default(uuid())
  name        String
  code        String? // Subject code like "MATH101"
  description String?
  credits     Int?
  isCore      Boolean  @default(true)
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  schoolId String
  school   School @relation(fields: [schoolId], references: [id], onDelete: Cascade)

  departmentId String?
  department   Department? @relation(fields: [departmentId], references: [id])

  classes            ClassSubject[]
  teacherAssignments TeacherAssignment[]
  timetableSlots     TimetableSlot[]
  attendanceRecords  AttendanceRecord[]
  gradeRecords       GradeRecord[]
  examSubjects       ExamSubject[]
  assignments        Assignment[]
  examResults        ExamResult[]
  examSchedules      ExamSchedule[]

  @@unique([schoolId, code])
}

// Class-Subject relationship (many-to-many)
model ClassSubject {
  id String @id @default(uuid())

  classId String
  class   Class  @relation(fields: [classId], references: [id], onDelete: Cascade)

  subjectId String
  subject   Subject @relation(fields: [subjectId], references: [id], onDelete: Cascade)

  @@unique([classId, subjectId])
}

// Student Enrollment
model StudentEnrollment {
  id               String    @id @default(uuid())
  enrollmentNumber String    @unique
  enrollmentDate   DateTime  @default(now())
  isActive         Boolean   @default(true)
  graduationDate   DateTime?

  // Relations
  studentId String
  student   Student @relation(fields: [studentId], references: [id], onDelete: Cascade)

  schoolId String
  school   School @relation(fields: [schoolId], references: [id], onDelete: Cascade)

  classId String
  class   Class  @relation(fields: [classId], references: [id], onDelete: Cascade)

  academicYearId String
  academicYear   AcademicYear @relation(fields: [academicYearId], references: [id], onDelete: Cascade)

  @@unique([studentId, academicYearId])
}

// Teacher Assignment
model TeacherAssignment {
  id             String    @id @default(uuid())
  isClassTeacher Boolean   @default(false)
  startDate      DateTime  @default(now())
  endDate        DateTime?
  isActive       Boolean   @default(true)

  // Relations
  teacherId String
  teacher   Teacher @relation(fields: [teacherId], references: [id], onDelete: Cascade)

  subjectId String?
  subject   Subject? @relation(fields: [subjectId], references: [id])

  departmentId String?
  department   Department? @relation(fields: [departmentId], references: [id])

  @@unique([teacherId, subjectId])
}

// Parent-Student relationship
model StudentParent {
  id           String   @id @default(uuid())
  relationship String // Father, Mother, Guardian, etc.
  isPrimary    Boolean  @default(false)
  createdAt    DateTime @default(now())

  // Relations
  studentId String
  student   Student @relation(fields: [studentId], references: [id], onDelete: Cascade)

  parentId String
  parent   Parent @relation(fields: [parentId], references: [id], onDelete: Cascade)

  @@unique([studentId, parentId])
}

// Timetable Management
model TimetableSlot {
  id           String @id @default(uuid())
  dayOfWeek    Int // 1-7 (Monday to Sunday)
  periodNumber Int // 1, 2, 3, etc.
  startTime    String // "09:00"
  endTime      String // "09:45"

  // Relations
  classId String
  class   Class  @relation(fields: [classId], references: [id], onDelete: Cascade)

  subjectId String
  subject   Subject @relation(fields: [subjectId], references: [id], onDelete: Cascade)

  teacherId String
  teacher   Teacher @relation(fields: [teacherId], references: [id], onDelete: Cascade)

  @@unique([classId, dayOfWeek, periodNumber])
}

// Attendance Management
model AttendanceRecord {
  id         String           @id @default(uuid())
  date       DateTime         @db.Date
  status     AttendanceStatus
  remarks    String?
  recordedAt DateTime         @default(now())

  // Relations
  studentId String
  student   Student @relation(fields: [studentId], references: [id], onDelete: Cascade)

  schoolId String
  school   School @relation(fields: [schoolId], references: [id], onDelete: Cascade)

  classId String
  class   Class  @relation(fields: [classId], references: [id], onDelete: Cascade)

  subjectId String?
  subject   Subject? @relation(fields: [subjectId], references: [id])

  academicYearId String
  academicYear   AcademicYear @relation(fields: [academicYearId], references: [id], onDelete: Cascade)

  recordedById String
  recordedBy   Teacher @relation("AttendanceRecorder", fields: [recordedById], references: [id])

  @@unique([studentId, date, subjectId])
}

// Exam Management
model Exam {
  id           String     @id @default(uuid())
  name         String
  type         String // Midterm, Final, Quiz, etc.
  description  String?
  startDate    DateTime
  endDate      DateTime
  totalMarks   Int?
  passingMarks Int?
  status       ExamStatus @default(SCHEDULED)
  instructions String?    @db.Text
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt

  // Relations
  schoolId String
  school   School @relation(fields: [schoolId], references: [id], onDelete: Cascade)

  academicYearId String
  academicYear   AcademicYear @relation(fields: [academicYearId], references: [id], onDelete: Cascade)

  academicTermId String?
  academicTerm   AcademicTerm? @relation(fields: [academicTermId], references: [id])

  classes   ExamClass[]
  subjects  ExamSubject[]
  results   ExamResult[]
  schedules ExamSchedule[]
}

// Exam-Class relationship
model ExamClass {
  id String @id @default(uuid())

  examId String
  exam   Exam   @relation(fields: [examId], references: [id], onDelete: Cascade)

  classId String
  class   Class  @relation(fields: [classId], references: [id], onDelete: Cascade)

  @@unique([examId, classId])
}

// Exam-Subject relationship
model ExamSubject {
  id       String @id @default(uuid())
  maxMarks Int
  duration Int? // in minutes

  examId String
  exam   Exam   @relation(fields: [examId], references: [id], onDelete: Cascade)

  subjectId String
  subject   Subject @relation(fields: [subjectId], references: [id], onDelete: Cascade)

  @@unique([examId, subjectId])
}

// Exam Schedule
model ExamSchedule {
  id        String   @id @default(uuid())
  date      DateTime @db.Date
  startTime String
  endTime   String
  venue     String?

  examId String
  exam   Exam   @relation(fields: [examId], references: [id], onDelete: Cascade)

  subjectId String
  subject   Subject @relation(fields: [subjectId], references: [id], onDelete: Cascade)

  @@unique([examId, subjectId, date])
}

// Exam Results
model ExamResult {
  id            String   @id @default(uuid())
  marksObtained Float
  grade         String?
  remarks       String?
  isAbsent      Boolean  @default(false)
  submittedAt   DateTime @default(now())

  // Relations
  examId String
  exam   Exam   @relation(fields: [examId], references: [id], onDelete: Cascade)

  studentId String
  student   Student @relation(fields: [studentId], references: [id], onDelete: Cascade)

  subjectId String
  subject   Subject @relation(fields: [subjectId], references: [id], onDelete: Cascade)

  @@unique([examId, studentId, subjectId])
}

// Grade Records (for continuous assessment)
model GradeRecord {
  id         String   @id @default(uuid())
  marks      Float
  maxMarks   Float
  grade      String?
  percentage Float?
  remarks    String?
  recordedAt DateTime @default(now())

  // Relations
  studentId String
  student   Student @relation(fields: [studentId], references: [id], onDelete: Cascade)

  subjectId String
  subject   Subject @relation(fields: [subjectId], references: [id], onDelete: Cascade)

  academicYearId String
  academicYear   AcademicYear @relation(fields: [academicYearId], references: [id], onDelete: Cascade)

  academicTermId String?
  academicTerm   AcademicTerm? @relation(fields: [academicTermId], references: [id])

  assignmentId String?
  assignment   Assignment? @relation(fields: [assignmentId], references: [id])

  recordedById String
  recordedBy   Teacher @relation("GradeRecorder", fields: [recordedById], references: [id])
}

// Assignment Management
model Assignment {
  id           String   @id @default(uuid())
  title        String
  description  String?  @db.Text
  dueDate      DateTime
  maxMarks     Float
  instructions String?  @db.Text
  isActive     Boolean  @default(true)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  subjectId String
  subject   Subject @relation(fields: [subjectId], references: [id], onDelete: Cascade)

  teacherId String
  teacher   Teacher @relation(fields: [teacherId], references: [id])

  gradeRecords GradeRecord[]
}

// Fee Management
model FeeStructure {
  id          String   @id @default(uuid())
  name        String // Tuition, Lab, Sports, etc.
  description String?
  amount      Decimal  @db.Decimal(10, 2)
  frequency   String // Monthly, Semester, Annual, One-time
  dueDay      Int? // Day of month for monthly fees
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  schoolId String
  school   School @relation(fields: [schoolId], references: [id], onDelete: Cascade)

  academicYearId String
  academicYear   AcademicYear @relation(fields: [academicYearId], references: [id], onDelete: Cascade)

  payments FeePayment[]

  @@unique([schoolId, academicYearId, name])
}

// Fee Payments
model FeePayment {
  id            String    @id @default(uuid())
  amount        Decimal   @db.Decimal(10, 2)
  paymentDate   DateTime  @default(now())
  dueDate       DateTime?
  status        FeeStatus @default(PENDING)
  paymentMethod String? // Cash, Card, Bank Transfer, etc.
  transactionId String?
  receiptNumber String?   @unique
  remarks       String?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Relations
  studentId String
  student   Student @relation(fields: [studentId], references: [id], onDelete: Cascade)

  feeStructureId String
  feeStructure   FeeStructure @relation(fields: [feeStructureId], references: [id], onDelete: Cascade)

  recordedById String?
  recordedBy   Staff?  @relation("PaymentRecorder", fields: [recordedById], references: [id])
}

// Event Management
model Event {
  id                String    @id @default(uuid())
  title             String
  description       String?   @db.Text
  startDate         DateTime
  endDate           DateTime?
  startTime         String?
  endTime           String?
  location          String?
  isAllDay          Boolean   @default(false)
  isPublic          Boolean   @default(true)
  type              EventType
  isRecurring       Boolean   @default(false)
  recurrencePattern String? // JSON for recurrence rules
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  // Relations
  schoolId String
  school   School @relation(fields: [schoolId], references: [id], onDelete: Cascade)

  academicYearId String?
  academicYear   AcademicYear? @relation(fields: [academicYearId], references: [id])

  academicTermId String?
  academicTerm   AcademicTerm? @relation(fields: [academicTermId], references: [id])

  createdById String
  createdBy   Teacher @relation("EventCreator", fields: [createdById], references: [id])
}

// Notification System
model Notification {
  id          String             @id @default(uuid())
  title       String
  message     String             @db.Text
  type        NotificationType
  status      NotificationStatus @default(UNREAD)
  isGlobal    Boolean            @default(false)
  scheduledAt DateTime?
  sentAt      DateTime?
  createdAt   DateTime           @default(now())

  // Relations
  recipientId String
  recipient   User   @relation("NotificationRecipient", fields: [recipientId], references: [id], onDelete: Cascade)

  schoolId String?
  school   School? @relation(fields: [schoolId], references: [id], onDelete: Cascade)

  senderId String?
  sender   User?   @relation("NotificationSender", fields: [senderId], references: [id])
}

// Disciplinary Records
model DisciplinaryRecord {
  id           String   @id @default(uuid())
  incidentDate DateTime
  description  String   @db.Text
  action       String // Warning, Suspension, etc.
  severity     String // Minor, Major, Severe
  isResolved   Boolean  @default(false)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  studentId String
  student   Student @relation(fields: [studentId], references: [id], onDelete: Cascade)

  reportedById String
  reportedBy   Teacher @relation("DisciplinaryReporter", fields: [reportedById], references: [id])
}

// Subscription History
model SubscriptionHistory {
  id        String             @id @default(uuid())
  planName  String
  planType  String // monthly, yearly
  amount    Decimal            @db.Decimal(10, 2)
  startDate DateTime
  endDate   DateTime
  status    SubscriptionStatus
  paymentId String?
  createdAt DateTime           @default(now())

  // Relations
  institutionId String
  institution   Institution @relation(fields: [institutionId], references: [id], onDelete: Cascade)
}

// Active Subscription Management
model Subscription {
  id                String             @id @default(uuid())
  institutionId     String             @unique
  pricingPlanId     String
  status            SubscriptionStatus @default(TRIAL)
  billingCycle      String // monthly, yearly
  amount            Decimal            @db.Decimal(10, 2)
  currency          String             @default("USD")
  startDate         DateTime
  endDate           DateTime?
  nextBillingDate   DateTime?
  autoRenew         Boolean            @default(true)
  trialEndDate      DateTime?
  cancelledAt       DateTime?
  cancellationReason String?          @db.Text
  
  // Payment tracking
  lastPaymentDate   DateTime?
  lastPaymentAmount Decimal?           @db.Decimal(10, 2)
  failedPaymentCount Int              @default(0)
  
  // Usage tracking
  currentStudents   Int                @default(0)
  currentTeachers   Int                @default(0)
  storageUsed       Int                @default(0) // in MB
  
  // Metadata
  notes             String?            @db.Text
  createdAt         DateTime           @default(now())
  updatedAt         DateTime           @updatedAt
  
  // Relations
  institution       Institution        @relation(fields: [institutionId], references: [id], onDelete: Cascade)
  pricingPlan       PricingPlan        @relation(fields: [pricingPlanId], references: [id])
  payments          Payment[]
  
  @@index([institutionId])
  @@index([status])
  @@index([nextBillingDate])
}

// Payment Management
model Payment {
  id              String        @id @default(uuid())
  subscriptionId  String
  amount          Decimal       @db.Decimal(10, 2)
  currency        String        @default("USD")
  status          PaymentStatus @default(PENDING)
  paymentMethod   String? // credit_card, bank_transfer, mobile_money, etc.
  transactionId   String?       @unique
  gatewayResponse String?       @db.Text // JSON response from payment gateway
  
  // Payment details
  description     String?
  invoiceNumber   String?       @unique
  dueDate         DateTime?
  paidAt          DateTime?
  failureReason   String?       @db.Text
  
  // Billing period
  billingStart    DateTime
  billingEnd      DateTime
  
  // Metadata
  metadata        String?       @db.Text // JSON for additional data
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  
  // Relations
  subscription    Subscription  @relation(fields: [subscriptionId], references: [id], onDelete: Cascade)
  
  @@index([subscriptionId])
  @@index([status])
  @@index([transactionId])
  @@index([invoiceNumber])
}

// Payment Status Enum
enum PaymentStatus {
  PENDING
  PROCESSING
  PAID
  FAILED
  CANCELLED
  REFUNDED
  PARTIALLY_REFUNDED
}

// Content Management Models for Super Admin
model Feature {
  id          String   @id @default(uuid())
  title       String
  description String   @db.Text
  icon        String?
  benefits    String?  @db.Text // JSON array
  category    String
  isActive    Boolean  @default(true)
  orderIndex  Int      @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@index([isActive])
  @@index([orderIndex])
}

model PricingPlan {
  id           String   @id @default(uuid())
  name         String
  description  String?  @db.Text
  price        Decimal  @db.Decimal(10, 2)
  currency     String   @default("USD")
  billing      String // monthly, yearly
  features     String?  @db.Text // JSON array
  isPopular    Boolean  @default(false)
  isActive     Boolean  @default(true)
  orderIndex   Int      @default(0)
  maxUsers     Int?
  maxStudents  Int?
  maxStorage   Int? // in GB
  supportLevel String? // Basic, Premium, Enterprise
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  
  // Relations
  subscriptions Subscription[]
  
  @@index([isActive])
  @@index([orderIndex])
}

model Testimonial {
  id         String   @id @default(uuid())
  name       String
  position   String?
  company    String?
  content    String   @db.Text
  rating     Int      @default(5)
  avatar     String?
  isActive   Boolean  @default(true)
  orderIndex Int      @default(0)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  
  @@index([isActive])
  @@index([orderIndex])
}

// Support System
model SupportTicket {
  id           String    @id @default(uuid())
  ticketNumber String    @unique
  subject      String
  description  String    @db.Text
  status       String    @default("open") // open, in_progress, resolved, closed
  priority     String    @default("medium") // low, medium, high, urgent
  category     String?
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  resolvedAt   DateTime?

  // Relations
  userId String
  user   User   @relation("SupportTickets", fields: [userId], references: [id], onDelete: Cascade)

  assignedToId String?
  assignedTo   User?   @relation("AssignedTickets", fields: [assignedToId], references: [id])

  institutionId String?
  institution   Institution? @relation("InstitutionSupport", fields: [institutionId], references: [id])

  responses SupportResponse[]
}

model SupportResponse {
  id         String   @id @default(uuid())
  message    String   @db.Text
  isInternal Boolean  @default(false)
  createdAt  DateTime @default(now())

  // Relations
  ticketId String
  ticket   SupportTicket @relation(fields: [ticketId], references: [id], onDelete: Cascade)

  userId String
  user   User   @relation("SupportResponses", fields: [userId], references: [id], onDelete: Cascade)
}

// Report Management System
model Report {
  id                   String     @id @default(uuid())
  name                 String
  type                 ReportType
  description          String?    @db.Text
  generatedByTeacherId String?
  generatedByStaffId   String?
  generatedOn          DateTime   @default(now())
  format               String     @default("PDF") // PDF, Excel, CSV
  filePath             String?
  fileUrl              String?
  parameters           String?    @db.Text // JSON parameters used for generation
  status               String     @default("completed") // generating, completed, failed
  isPublic             Boolean    @default(false)

  // Relations
  generatedByTeacher Teacher? @relation("GeneratedReports", fields: [generatedByTeacherId], references: [id])
  generatedByStaff   Staff?   @relation("GeneratedReports", fields: [generatedByStaffId], references: [id])
  schoolId           String
  school             School   @relation(fields: [schoolId], references: [id], onDelete: Cascade)

  expiresAt DateTime?
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
}

// Library Management System
model LibraryBook {
  id              String   @id @default(uuid())
  title           String
  author          String
  isbn            String?  @unique
  publisher       String?
  publishedYear   Int?
  genre           String?
  language        String?  @default("English")
  totalCopies     Int      @default(1)
  availableCopies Int      @default(1)
  location        String? // Shelf location
  description     String?  @db.Text
  coverImageUrl   String?
  isActive        Boolean  @default(true)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  schoolId String
  school   School @relation("LibraryBooks", fields: [schoolId], references: [id], onDelete: Cascade)

  borrowRecords LibraryBorrowRecord[]
}

model LibraryBorrowRecord {
  id         String    @id @default(uuid())
  borrowDate DateTime  @default(now())
  dueDate    DateTime
  returnDate DateTime?
  status     String    @default("borrowed") // borrowed, returned, overdue, lost
  fineAmount Decimal?  @db.Decimal(10, 2)
  remarks    String?
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt

  // Relations
  bookId String
  book   LibraryBook @relation(fields: [bookId], references: [id], onDelete: Cascade)

  borrowerId String
  borrower   Student @relation(fields: [borrowerId], references: [id], onDelete: Cascade)

  issuedById String?
  issuedBy   Teacher? @relation("LibraryIssuer", fields: [issuedById], references: [id])

  issuedByStaffId String?
  issuedByStaff   Staff?  @relation("StaffLibraryIssuer", fields: [issuedByStaffId], references: [id])
}

// System Settings and Configuration
model SystemSetting {
  id          String   @id @default(uuid())
  key         String   @unique
  value       String   @db.Text
  type        String // string, number, boolean, json
  category    String // general, email, notification, storage, security
  description String?
  isEditable  Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

// Email Templates for notifications
model EmailTemplate {
  id          String   @id @default(uuid())
  name        String   @unique
  subject     String
  htmlContent String   @db.Text
  textContent String?  @db.Text
  variables   String?  @db.Text // JSON array of available variables
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}
