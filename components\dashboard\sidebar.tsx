"use client"

import { useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"

import {
  Users,
  GraduationCap,
  BookOpen,
  Calendar,
  CreditCard,
  BarChart,
  Settings,
  Home,
  Menu,
  Building,
  UserCircle,

  Clock,
  FileText,
  School,
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { She<PERSON>, <PERSON>etContent, SheetTrigger } from "@/components/ui/sheet"
import { useMobile } from "@/hooks/use-mobile"
import { useTenant } from "@/contexts/tenant-context"
import { InstitutionSwitcher } from "@/components/dashboard/institution-switcher"
import { LogoutButton } from "@/components/auth/logout-button"

interface SidebarProps {
  className?: string
}

export function Sidebar({ className }: SidebarProps) {
  const pathname = usePathname()
  const isMobile = useMobile()
  const [open, setOpen] = useState(false)
  const { currentUser, currentInstitution } = useTenant()


  // Define routes based on user role
  const getRoutes = () => {
    const commonRoutes = [
      {
        label: "Dashboard",
        icon: Home,
        href: "/dashboard",
        active: pathname === "/dashboard",
      },
    ]

    const adminRoutes = [
      ...commonRoutes,
      {
        label: "Students",
        icon: GraduationCap,
        href: "/dashboard/students",
        active: pathname.startsWith("/dashboard/students"),
      },
      {
        label: "Teachers",
        icon: UserCircle,
        href: "/dashboard/teachers",
        active: pathname.startsWith("/dashboard/teachers"),
      },
      {
        label: "Staff",
        icon: Users,
        href: "/dashboard/staff",
        active: pathname.startsWith("/dashboard/staff"),
      },
      {
        label: "Classes",
        icon: School,
        href: "/dashboard/classes",
        active: pathname.startsWith("/dashboard/classes"),
      },
      {
        label: "Subjects",
        icon: BookOpen,
        href: "/dashboard/subjects",
        active: pathname.startsWith("/dashboard/subjects"),
      },
      {
        label: "Timetable",
        icon: Clock,
        href: "/dashboard/timetable",
        active: pathname.startsWith("/dashboard/timetable"),
      },
      {
        label: "Attendance",
        icon: Calendar,
        href: "/dashboard/attendance",
        active: pathname.startsWith("/dashboard/attendance"),
      },
      {
        label: "Exams & Grades",
        icon: FileText,
        href: "/dashboard/exams",
        active: pathname.startsWith("/dashboard/exams"),
      },
      {
        label: "Fees",
        icon: CreditCard,
        href: "/dashboard/fees",
        active: pathname.startsWith("/dashboard/fees"),
      },
      {
        label: "Reports",
        icon: BarChart,
        href: "/dashboard/reports",
        active: pathname.startsWith("/dashboard/reports"),
      },
      {
        label: "Institution",
        icon: Building,
        href: "/dashboard/institution",
        active: pathname.startsWith("/dashboard/institution"),
      },
      {
        label: "Settings",
        icon: Settings,
        href: "/dashboard/settings",
        active: pathname.startsWith("/dashboard/settings"),
      },
    ]

    const teacherRoutes = [
      ...commonRoutes,
      {
        label: "My Classes",
        icon: School,
        href: "/dashboard/my-classes",
        active: pathname.startsWith("/dashboard/my-classes"),
      },
      {
        label: "Students",
        icon: GraduationCap,
        href: "/dashboard/students",
        active: pathname.startsWith("/dashboard/students"),
      },
      {
        label: "Attendance",
        icon: Calendar,
        href: "/dashboard/attendance",
        active: pathname.startsWith("/dashboard/attendance"),
      },
      {
        label: "Grades",
        icon: FileText,
        href: "/dashboard/grades",
        active: pathname.startsWith("/dashboard/grades"),
      },
      {
        label: "Timetable",
        icon: Clock,
        href: "/dashboard/timetable",
        active: pathname.startsWith("/dashboard/timetable"),
      },
      {
        label: "Settings",
        icon: Settings,
        href: "/dashboard/settings",
        active: pathname.startsWith("/dashboard/settings"),
      },
    ]

    const studentRoutes = [
      ...commonRoutes,
      {
        label: "My Classes",
        icon: School,
        href: "/dashboard/my-classes",
        active: pathname.startsWith("/dashboard/my-classes"),
      },
      {
        label: "Attendance",
        icon: Calendar,
        href: "/dashboard/attendance",
        active: pathname.startsWith("/dashboard/attendance"),
      },
      {
        label: "Grades",
        icon: FileText,
        href: "/dashboard/grades",
        active: pathname.startsWith("/dashboard/grades"),
      },
      {
        label: "Timetable",
        icon: Clock,
        href: "/dashboard/timetable",
        active: pathname.startsWith("/dashboard/timetable"),
      },
      {
        label: "Fees",
        icon: CreditCard,
        href: "/dashboard/fees",
        active: pathname.startsWith("/dashboard/fees"),
      },
      {
        label: "Settings",
        icon: Settings,
        href: "/dashboard/settings",
        active: pathname.startsWith("/dashboard/settings"),
      },
    ]

    const parentRoutes = [
      ...commonRoutes,
      {
        label: "My Children",
        icon: GraduationCap,
        href: "/dashboard/children",
        active: pathname.startsWith("/dashboard/children"),
      },
      {
        label: "Attendance",
        icon: Calendar,
        href: "/dashboard/attendance",
        active: pathname.startsWith("/dashboard/attendance"),
      },
      {
        label: "Grades",
        icon: FileText,
        href: "/dashboard/grades",
        active: pathname.startsWith("/dashboard/grades"),
      },
      {
        label: "Fees",
        icon: CreditCard,
        href: "/dashboard/fees",
        active: pathname.startsWith("/dashboard/fees"),
      },
      {
        label: "Settings",
        icon: Settings,
        href: "/dashboard/settings",
        active: pathname.startsWith("/dashboard/settings"),
      },
    ]

    const staffRoutes = [
      ...commonRoutes,
      {
        label: "Students",
        icon: GraduationCap,
        href: "/dashboard/students",
        active: pathname.startsWith("/dashboard/students"),
      },
      {
        label: "Fees",
        icon: CreditCard,
        href: "/dashboard/fees",
        active: pathname.startsWith("/dashboard/fees"),
      },
      {
        label: "Reports",
        icon: BarChart,
        href: "/dashboard/reports",
        active: pathname.startsWith("/dashboard/reports"),
      },
      {
        label: "Settings",
        icon: Settings,
        href: "/dashboard/settings",
        active: pathname.startsWith("/dashboard/settings"),
      },
    ]

    // Return routes based on user role
    switch (currentUser?.role) {
      case "INSTITUTION_ADMIN":
      case "SCHOOL_ADMIN":
      case "ADMIN":
        return adminRoutes
      case "TEACHER":
        return teacherRoutes
      case "STUDENT":
        return studentRoutes
      case "PARENT":
        return parentRoutes
      case "STAFF":
        return staffRoutes
      case "SUPER_ADMIN":
        // Super admins should not be using the dashboard sidebar
        // They should be redirected to /super-admin
        return commonRoutes
      default:
        return commonRoutes
    }
  }

  const routes = getRoutes()

  const SidebarContent = (
    <div className={cn(
      "h-full flex flex-col border-r", 
      "bg-white dark:bg-gray-950 dark:border-gray-800",
      className
    )}>
      <div className="h-16 flex items-center px-6 border-b dark:border-gray-800">
        <Link href="/dashboard" className="flex items-center">
          <span className="text-xl font-bold text-emerald-600 dark:text-emerald-500">Edusync</span>
        </Link>
      </div>
      {isMobile && (
        <div className="p-4">
          <InstitutionSwitcher />
        </div>
      )}
      <ScrollArea className="flex-1 py-4">
        {currentInstitution && (
          <div className="px-4 mb-4">
            <div className="bg-emerald-50 dark:bg-emerald-950/50 p-3 rounded-lg">
              <p className="text-xs text-emerald-700 dark:text-emerald-400 font-medium">Current Institution</p>
              <p className="text-sm font-medium truncate dark:text-gray-200">{currentInstitution.name}</p>
            </div>
          </div>
        )}
        <nav className="grid gap-1 px-2">
          {routes.map((route) => (
            <Link
              key={route.href}
              href={route.href}
              onClick={() => setOpen(false)}
              className={cn(
                "flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors",
                route.active 
                  ? "bg-emerald-50 text-emerald-700 dark:bg-emerald-950/50 dark:text-emerald-400" 
                  : "text-gray-500 hover:bg-gray-100 hover:text-gray-900 dark:text-gray-400 dark:hover:bg-gray-800 dark:hover:text-gray-200",
              )}
            >
              <route.icon className={cn("h-5 w-5", route.active ? "text-emerald-700 dark:text-emerald-400" : "text-gray-400 dark:text-gray-500")} />
              {route.label}
            </Link>
          ))}
        </nav>
      </ScrollArea>
      <div className="mt-auto p-4 border-t dark:border-gray-800">
        <LogoutButton
          variant="outline"
          className="w-full justify-start dark:border-gray-700"
          showConfirmation={true}
        />
      </div>
    </div>
  )

  if (isMobile) {
    return (
      <>
        <Sheet open={open} onOpenChange={setOpen}>
          <SheetTrigger asChild>
            <Button variant="outline" size="icon" className="md:hidden">
              <Menu className="h-5 w-5" />
              <span className="sr-only">Toggle Menu</span>
            </Button>
          </SheetTrigger>
          <SheetContent side="left" className="p-0 w-72">
            {SidebarContent}
          </SheetContent>
        </Sheet>
      </>
    )
  }

  return SidebarContent
}
