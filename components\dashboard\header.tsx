"use client"

import { useState } from "react"
import Link from "next/link"
import { Search, Sun, Moon } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { InstitutionSwitcher } from "@/components/dashboard/institution-switcher"
import { NotificationCenter } from "@/components/notifications/notification-center"
import { useTenant } from "@/contexts/tenant-context"
import { useTheme } from "next-themes"
import { LogoutMenuItem } from "@/components/auth/logout-button"

export function Header() {
  const [searchQuery, setSearchQuery] = useState("")
  const { currentUser, isSuperAdmin, currentInstitution } = useTenant()
  const { theme, setTheme } = useTheme()

  const userInitials = currentUser ? `${currentUser.firstName.charAt(0)}${currentUser.lastName.charAt(0)}` : "U"

  return (
    <header className="sticky top-0 z-30 flex h-16 items-center gap-4 border-b bg-background px-6">
      <div className="hidden md:flex md:items-center md:gap-4">
        <InstitutionSwitcher />
      </div>
      <div className="hidden md:block w-full max-w-md">
        <div className="relative">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search..."
            className="w-full bg-muted/50 pl-8 focus-visible:ring-emerald-500"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>
      <div className="ml-auto flex items-center gap-4">
        <Button
          variant="outline"
          size="icon"
          onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
          aria-label="Toggle theme"
        >
          {theme === "dark" ? <Sun className="h-5 w-5" /> : <Moon className="h-5 w-5" />}
        </Button>
        <NotificationCenter />
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" className="rounded-full">
              <Avatar>
                <AvatarImage src="/placeholder.svg?height=32&width=32" alt="User" />
                <AvatarFallback>{userInitials}</AvatarFallback>
              </Avatar>
              <span className="sr-only">User menu</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>
              {currentUser ? `${currentUser.firstName} ${currentUser.lastName}` : "My Account"}
              {currentUser && (
                <p className="text-xs text-muted-foreground mt-1">{currentUser.role.replace("_", " ")}</p>
              )}
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem asChild>
              <Link href="/dashboard/institution-profile">Institution Profile</Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link href="/dashboard/settings">Settings</Link>
            </DropdownMenuItem>
            {isSuperAdmin && (
              <>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href="/super-admin">Super Admin Dashboard</Link>
                </DropdownMenuItem>
              </>
            )}
            <DropdownMenuSeparator />
            <LogoutMenuItem showConfirmation={true} />
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  )
}
