import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { jwtVerify } from 'jose'

// Define role-based routes
const roleBasedRoutes = {
  SUPER_ADMIN: '/super-admin',
  INSTITUTION_ADMIN: '/dashboard',
  SCHOOL_ADMIN: '/dashboard',
  TEACHER: '/dashboard',
  STUDENT: '/dashboard',
  PARENT: '/dashboard',
  STAFF: '/dashboard',
}

// Define protected routes that require authentication
const protectedRoutes = [
  '/dashboard',
  '/super-admin',
  '/onboarding',
  '/debug-auth',
]

async function verifyToken(token: string) {
  try {
    const secret = new TextEncoder().encode(process.env.JWT_SECRET)
    const { payload } = await jwtVerify(token, secret)
    return payload
  } catch (error) {
    return null
  }
}

export async function middleware(request: NextRequest) {
  const sessionToken = request.cookies.get('session')?.value
  const accessToken = request.cookies.get('accessToken')?.value
  const path = request.nextUrl.pathname

  // Allow access to home page for non-logged-in users
  if (path === '/' && !sessionToken && !accessToken) {
    return NextResponse.next()
  }

  // Check if the current path is a protected route
  const isProtectedRoute = protectedRoutes.some(route => path.startsWith(route))

  // If trying to access protected routes without any tokens
  if (isProtectedRoute && !sessionToken && !accessToken) {
    return NextResponse.redirect(new URL('/auth/login', request.url))
  }

  // If we have either token, verify the session token (which contains user info)
  if (sessionToken || accessToken) {
    let payload = null

    // Try to verify session token first
    if (sessionToken) {
      payload = await verifyToken(sessionToken)
    }

    // If session token is invalid but we have access token,
    // the user is still authenticated (access token will be verified by API routes)
    if (!payload && accessToken) {
      // For middleware purposes, we'll allow the request to continue
      // The API routes will handle access token validation
      return NextResponse.next()
    }

    if (payload) {
      // If user is logged in and trying to access home page or auth pages
      if (path === '/' || path.startsWith('/auth')) {
        const role = payload.role as keyof typeof roleBasedRoutes
        const redirectUrl = roleBasedRoutes[role] || '/dashboard'
        return NextResponse.redirect(new URL(redirectUrl, request.url))
      }

      // Handle role-specific access control
      const role = payload.role as keyof typeof roleBasedRoutes

      // Super admins can only access super-admin routes
      if (role === 'SUPER_ADMIN' && !path.startsWith('/super-admin')) {
        return NextResponse.redirect(new URL('/super-admin', request.url))
      }

      // Non-super-admin users cannot access super-admin routes
      if (role !== 'SUPER_ADMIN' && path.startsWith('/super-admin')) {
        return NextResponse.redirect(new URL('/dashboard', request.url))
      }
    } else if (isProtectedRoute) {
      // If we can't verify the session token and it's a protected route, redirect to login
      return NextResponse.redirect(new URL('/auth/login', request.url))
    }
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
}