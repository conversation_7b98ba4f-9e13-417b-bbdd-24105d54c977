const express = require('express');
const router = express.Router();
const {
  getAllDepartments,
  getDepartmentById,
  createDepartment,
  updateDepartment,
  deleteDepartment
} = require('./departments.controller');
const { authenticateToken, authorizeRoles } = require('../../../middleware/auth');

// Apply authentication to all routes
router.use(authenticateToken);

// GET /api/v1/departments - Get all departments
router.get('/', authorizeRoles(['SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN', 'TEACHER']), getAllDepartments);

// GET /api/v1/departments/:id - Get department by ID
router.get('/:id', authorizeRoles(['SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN', 'TEACHER']), getDepartmentById);

// POST /api/v1/departments - Create new department
router.post('/', authorizeRoles(['SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN']), createDepartment);

// PUT /api/v1/departments/:id - Update department
router.put('/:id', authorizeRoles(['SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN']), updateDepartment);

// DELETE /api/v1/departments/:id - Delete department
router.delete('/:id', authorizeRoles(['SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN']), deleteDepartment);

module.exports = router;
