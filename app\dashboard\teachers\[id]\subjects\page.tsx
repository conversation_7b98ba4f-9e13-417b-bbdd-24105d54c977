"use client"

import { Calendar } from "@/components/ui/calendar"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { ArrowLeft, BookOpen, Users, FileText, CheckCircle, Clock } from "lucide-react"
import Link from "next/link"

export default function TeacherSubjectsPage({ params }: { params: { id: string } }) {
  const teacherId = params.id

  // This would normally come from a database
  const teacher = {
    id: teacherId,
    name: "Dr. <PERSON>",
    department: "Mathematics",
    position: "Senior Mathematics Teacher",
    employeeId: "EMP-2015-042",
  }

  // Sample subjects data
  const subjects = [
    {
      name: "Algebra II",
      classes: ["10A", "10B"],
      students: 64,
      averageGrade: "B+",
      completionRate: "85%",
      description: "Advanced algebraic concepts including functions, equations, and inequalities.",
      topics: [
        { name: "Functions and Relations", status: "Completed", completion: "100%" },
        { name: "Quadratic Equations", status: "Completed", completion: "100%" },
        { name: "Polynomial Functions", status: "In Progress", completion: "60%" },
        { name: "Rational Expressions", status: "Not Started", completion: "0%" },
        { name: "Exponential and Logarithmic Functions", status: "Not Started", completion: "0%" },
      ],
      resources: [
        { name: "Algebra II Textbook", type: "Book", author: "Pearson Education" },
        { name: "Algebra II Workbook", type: "Workbook", author: "McGraw Hill" },
        { name: "Online Practice Problems", type: "Digital", author: "Khan Academy" },
      ],
      assessments: [
        { name: "Quiz 1: Functions", date: "Sep 15, 2023", averageScore: "88%" },
        { name: "Quiz 2: Quadratic Equations", date: "Sep 29, 2023", averageScore: "85%" },
        { name: "Midterm Exam", date: "Oct 20, 2023", averageScore: "Upcoming" },
      ],
    },
    {
      name: "Calculus",
      classes: ["11A"],
      students: 32,
      averageGrade: "B",
      completionRate: "78%",
      description: "Introduction to differential and integral calculus, including limits, derivatives, and integrals.",
      topics: [
        { name: "Limits and Continuity", status: "Completed", completion: "100%" },
        { name: "Derivatives", status: "Completed", completion: "100%" },
        { name: "Applications of Derivatives", status: "In Progress", completion: "75%" },
        { name: "Integrals", status: "Not Started", completion: "0%" },
        { name: "Applications of Integrals", status: "Not Started", completion: "0%" },
      ],
      resources: [
        { name: "Calculus: Early Transcendentals", type: "Book", author: "James Stewart" },
        { name: "Calculus Problem Set", type: "Workbook", author: "MIT OpenCourseWare" },
        { name: "Calculus Video Lectures", type: "Digital", author: "Professor Leonard" },
      ],
      assessments: [
        { name: "Quiz 1: Limits", date: "Sep 12, 2023", averageScore: "82%" },
        { name: "Quiz 2: Derivatives", date: "Sep 26, 2023", averageScore: "80%" },
        { name: "Midterm Exam", date: "Oct 17, 2023", averageScore: "Upcoming" },
      ],
    },
    {
      name: "Statistics",
      classes: ["12A"],
      students: 32,
      averageGrade: "A-",
      completionRate: "90%",
      description: "Introduction to statistical concepts, data analysis, probability, and statistical inference.",
      topics: [
        { name: "Descriptive Statistics", status: "Completed", completion: "100%" },
        { name: "Probability", status: "Completed", completion: "100%" },
        { name: "Probability Distributions", status: "Completed", completion: "100%" },
        { name: "Sampling and Inference", status: "In Progress", completion: "50%" },
        { name: "Hypothesis Testing", status: "Not Started", completion: "0%" },
      ],
      resources: [
        { name: "Statistics: Informed Decisions", type: "Book", author: "Michael Sullivan" },
        { name: "Statistics Problem Set", type: "Workbook", author: "College Board" },
        { name: "Statistical Analysis Software", type: "Digital", author: "StatCrunch" },
      ],
      assessments: [
        { name: "Quiz 1: Descriptive Statistics", date: "Sep 14, 2023", averageScore: "92%" },
        { name: "Quiz 2: Probability", date: "Sep 28, 2023", averageScore: "88%" },
        { name: "Midterm Exam", date: "Oct 19, 2023", averageScore: "Upcoming" },
      ],
    },
  ]

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between gap-4">
        <div>
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="icon" asChild>
              <Link href={`/dashboard/teachers/${teacherId}`}>
                <ArrowLeft className="h-4 w-4" />
                <span className="sr-only">Back to teacher profile</span>
              </Link>
            </Button>
            <h1 className="text-3xl font-bold tracking-tight">Teaching Subjects</h1>
          </div>
          <p className="text-muted-foreground ml-10">
            Subjects taught by {teacher.name} ({teacher.position})
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <FileText className="mr-2 h-4 w-4" />
            Generate Report
          </Button>
          <Button>
            <BookOpen className="mr-2 h-4 w-4" />
            Add Subject
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {subjects.map((subject, index) => (
          <Card key={index}>
            <CardHeader>
              <CardTitle>{subject.name}</CardTitle>
              <CardDescription>
                {subject.classes.map((c) => `Class ${c}`).join(", ")} • {subject.students} Students
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">Average Grade</p>
                    <p className="text-lg font-medium">{subject.averageGrade}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">Completion</p>
                    <p className="text-lg font-medium">{subject.completionRate}</p>
                  </div>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground mb-1">Curriculum Progress</p>
                  <div className="w-full bg-muted rounded-full h-2">
                    <div className="bg-primary h-2 rounded-full" style={{ width: subject.completionRate }}></div>
                  </div>
                </div>
                <p className="text-sm">{subject.description}</p>
              </div>
            </CardContent>
            <CardFooter>
              <Button
                variant="outline"
                className="w-full"
                onClick={() => document.getElementById(`subject-${index}`)?.scrollIntoView({ behavior: "smooth" })}
              >
                View Details
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>

      {subjects.map((subject, index) => (
        <Card key={index} id={`subject-${index}`} className="scroll-mt-6">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-2xl">{subject.name}</CardTitle>
                <CardDescription>
                  {subject.classes.map((c) => `Class ${c}`).join(", ")} • {subject.students} Students
                </CardDescription>
              </div>
              <Button variant="outline" size="sm">
                <FileText className="mr-2 h-4 w-4" />
                Edit Subject
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="curriculum" className="space-y-4">
              <TabsList>
                <TabsTrigger value="curriculum">Curriculum</TabsTrigger>
                <TabsTrigger value="students">Students</TabsTrigger>
                <TabsTrigger value="resources">Resources</TabsTrigger>
                <TabsTrigger value="assessments">Assessments</TabsTrigger>
              </TabsList>

              <TabsContent value="curriculum" className="space-y-4">
                <div className="space-y-4">
                  <div>
                    <h3 className="text-lg font-medium mb-2">Course Description</h3>
                    <p>{subject.description}</p>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium mb-2">Curriculum Topics</h3>
                    <div className="rounded-md border">
                      <div className="grid grid-cols-4 bg-muted/50 p-3 text-sm font-medium">
                        <div className="col-span-2">Topic</div>
                        <div>Status</div>
                        <div>Completion</div>
                      </div>
                      <div className="divide-y">
                        {subject.topics.map((topic, topicIndex) => (
                          <div key={topicIndex} className="grid grid-cols-4 p-3 text-sm">
                            <div className="col-span-2 font-medium">{topic.name}</div>
                            <div>
                              <span
                                className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${
                                  topic.status === "Completed"
                                    ? "bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400"
                                    : topic.status === "In Progress"
                                      ? "bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400"
                                      : "bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-400"
                                }`}
                              >
                                {topic.status === "Completed" && <CheckCircle className="mr-1 h-3 w-3" />}
                                {topic.status === "In Progress" && <Clock className="mr-1 h-3 w-3" />}
                                {topic.status}
                              </span>
                            </div>
                            <div>
                              <div className="flex items-center gap-2">
                                <div className="w-full max-w-24 bg-muted rounded-full h-2">
                                  <div
                                    className="bg-primary h-2 rounded-full"
                                    style={{ width: topic.completion }}
                                  ></div>
                                </div>
                                <span>{topic.completion}</span>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium">Overall Progress</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          <div className="flex items-center justify-between">
                            <span className="text-sm font-medium">Curriculum Completion</span>
                            <span className="text-sm font-medium">{subject.completionRate}</span>
                          </div>
                          <div className="w-full bg-muted rounded-full h-2">
                            <div
                              className="bg-primary h-2 rounded-full"
                              style={{ width: subject.completionRate }}
                            ></div>
                          </div>
                          <div className="grid grid-cols-3 gap-2 text-center pt-2">
                            <div>
                              <p className="text-xs text-muted-foreground">Completed</p>
                              <p className="font-medium">
                                {subject.topics.filter((t) => t.status === "Completed").length}
                              </p>
                            </div>
                            <div>
                              <p className="text-xs text-muted-foreground">In Progress</p>
                              <p className="font-medium">
                                {subject.topics.filter((t) => t.status === "In Progress").length}
                              </p>
                            </div>
                            <div>
                              <p className="text-xs text-muted-foreground">Not Started</p>
                              <p className="font-medium">
                                {subject.topics.filter((t) => t.status === "Not Started").length}
                              </p>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium">Upcoming Topics</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          {subject.topics
                            .filter((t) => t.status !== "Completed")
                            .slice(0, 3)
                            .map((topic, topicIndex) => (
                              <div key={topicIndex} className="flex items-center justify-between">
                                <div>
                                  <p className="font-medium">{topic.name}</p>
                                  <p className="text-xs text-muted-foreground">
                                    {topic.status === "In Progress" ? "Currently teaching" : "Upcoming"}
                                  </p>
                                </div>
                                <span
                                  className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${
                                    topic.status === "In Progress"
                                      ? "bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400"
                                      : "bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-400"
                                  }`}
                                >
                                  {topic.status === "In Progress" && <Clock className="mr-1 h-3 w-3" />}
                                  {topic.status}
                                </span>
                              </div>
                            ))}
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="students" className="space-y-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Student Performance</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="p-4 border rounded-lg text-center">
                          <p className="text-3xl font-bold">{subject.students}</p>
                          <p className="text-sm text-muted-foreground">Total Students</p>
                        </div>
                        <div className="p-4 border rounded-lg text-center">
                          <p className="text-3xl font-bold">{subject.averageGrade}</p>
                          <p className="text-sm text-muted-foreground">Average Grade</p>
                        </div>
                        <div className="p-4 border rounded-lg text-center">
                          <p className="text-3xl font-bold">92%</p>
                          <p className="text-sm text-muted-foreground">Attendance Rate</p>
                        </div>
                      </div>

                      <div>
                        <h3 className="text-sm font-medium mb-2">Grade Distribution</h3>
                        <div className="space-y-2">
                          {[
                            { grade: "A", percentage: "30%" },
                            { grade: "B", percentage: "45%" },
                            { grade: "C", percentage: "20%" },
                            { grade: "D", percentage: "5%" },
                            { grade: "F", percentage: "0%" },
                          ].map((item, index) => (
                            <div key={index}>
                              <div className="flex items-center justify-between mb-1">
                                <span className="text-sm font-medium">{item.grade}</span>
                                <span className="text-sm font-medium">{item.percentage}</span>
                              </div>
                              <div className="w-full bg-muted rounded-full h-2">
                                <div
                                  className={`h-2 rounded-full ${
                                    item.grade === "A"
                                      ? "bg-green-500"
                                      : item.grade === "B"
                                        ? "bg-blue-500"
                                        : item.grade === "C"
                                          ? "bg-amber-500"
                                          : item.grade === "D"
                                            ? "bg-orange-500"
                                            : "bg-red-500"
                                  }`}
                                  style={{ width: item.percentage }}
                                ></div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Top Performing Students</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {[
                        { name: "Emma Thompson", class: subject.classes[0], grade: "A+", score: "98%" },
                        { name: "Michael Chen", class: subject.classes[0], grade: "A", score: "95%" },
                        { name: "Sophia Rodriguez", class: subject.classes[0], grade: "A", score: "94%" },
                        { name: "James Wilson", class: subject.classes[0], grade: "A", score: "93%" },
                        { name: "Olivia Davis", class: subject.classes[0], grade: "A-", score: "91%" },
                      ].map((student, index) => (
                        <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                          <div className="flex items-center">
                            <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center text-primary font-medium mr-3">
                              {index + 1}
                            </div>
                            <div>
                              <p className="font-medium">{student.name}</p>
                              <p className="text-xs text-muted-foreground">Class {student.class}</p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="font-medium text-green-600 dark:text-green-400">{student.grade}</p>
                            <p className="text-xs text-muted-foreground">{student.score}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Button variant="outline" className="w-full">
                      <Users className="mr-2 h-4 w-4" />
                      View All Students
                    </Button>
                  </CardFooter>
                </Card>
              </TabsContent>

              <TabsContent value="resources" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Teaching Resources</CardTitle>
                    <CardDescription>Materials used for teaching {subject.name}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {subject.resources.map((resource, index) => (
                        <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                          <div className="flex items-center">
                            <BookOpen className="h-8 w-8 text-primary mr-4" />
                            <div>
                              <p className="font-medium">{resource.name}</p>
                              <p className="text-sm text-muted-foreground">
                                {resource.type} • By {resource.author}
                              </p>
                            </div>
                          </div>
                          <div className="flex gap-2">
                            <Button variant="outline" size="sm">
                              View
                            </Button>
                            <Button variant="outline" size="sm">
                              Share
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Button className="w-full">
                      <BookOpen className="mr-2 h-4 w-4" />
                      Add New Resource
                    </Button>
                  </CardFooter>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Lesson Plans</CardTitle>
                    <CardDescription>Weekly lesson plans for {subject.name}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {[
                        { week: "Week 1", topic: "Introduction to " + subject.name, status: "Completed" },
                        { week: "Week 2", topic: "Fundamental Concepts", status: "Completed" },
                        { week: "Week 3", topic: "Advanced Applications", status: "In Progress" },
                        { week: "Week 4", topic: "Problem Solving Techniques", status: "Upcoming" },
                        { week: "Week 5", topic: "Review and Assessment", status: "Upcoming" },
                      ].map((plan, index) => (
                        <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                          <div>
                            <p className="font-medium">
                              {plan.week}: {plan.topic}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              {plan.status === "Completed"
                                ? "Completed"
                                : plan.status === "In Progress"
                                  ? "Currently teaching"
                                  : "Upcoming"}
                            </p>
                          </div>
                          <div className="flex gap-2">
                            <Button variant="outline" size="sm">
                              View
                            </Button>
                            <Button variant="outline" size="sm">
                              Edit
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="assessments" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Assessments</CardTitle>
                    <CardDescription>Quizzes, tests, and exams for {subject.name}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {subject.assessments.map((assessment, index) => (
                        <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                          <div>
                            <p className="font-medium">{assessment.name}</p>
                            <p className="text-sm text-muted-foreground">Date: {assessment.date}</p>
                          </div>
                          <div className="text-right">
                            <p className="font-medium">
                              {assessment.averageScore === "Upcoming" ? (
                                <span className="text-amber-500">Upcoming</span>
                              ) : (
                                assessment.averageScore
                              )}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              {assessment.averageScore === "Upcoming" ? "Not yet taken" : "Average Score"}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Button className="w-full">
                      <FileText className="mr-2 h-4 w-4" />
                      Create New Assessment
                    </Button>
                  </CardFooter>
                </Card>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">Assessment Performance</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="h-60 w-full">
                        {/* This would be a chart in a real implementation */}
                        <div className="h-full w-full flex flex-col">
                          <div className="flex-1 grid grid-cols-3 gap-8 items-end pb-4">
                            {subject.assessments
                              .filter((a) => a.averageScore !== "Upcoming")
                              .map((assessment, index) => (
                                <div key={index} className="relative w-full">
                                  <div
                                    className="w-full bg-primary rounded-t-sm"
                                    style={{ height: `${Number.parseInt(assessment.averageScore)}%` }}
                                  ></div>
                                </div>
                              ))}
                          </div>
                          <div className="grid grid-cols-3 gap-8 text-xs text-center text-muted-foreground">
                            {subject.assessments
                              .filter((a) => a.averageScore !== "Upcoming")
                              .map((assessment, index) => (
                                <div key={index}>Quiz {index + 1}</div>
                              ))}
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">Upcoming Assessments</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {subject.assessments
                          .filter((a) => a.averageScore === "Upcoming")
                          .map((assessment, index) => (
                            <div key={index} className="flex items-center justify-between">
                              <div>
                                <p className="font-medium">{assessment.name}</p>
                                <p className="text-xs text-muted-foreground">Scheduled for {assessment.date}</p>
                              </div>
                              <Button variant="outline" size="sm">
                                Prepare
                              </Button>
                            </div>
                          ))}
                        {subject.assessments.filter((a) => a.averageScore === "Upcoming").length === 0 && (
                          <div className="text-center py-4">
                            <p className="text-muted-foreground">No upcoming assessments scheduled</p>
                          </div>
                        )}
                      </div>
                    </CardContent>
                    <CardFooter>
                      <Button variant="outline" className="w-full">
                        <Calendar className="mr-2 h-4 w-4" />
                        Schedule Assessment
                      </Button>
                    </CardFooter>
                  </Card>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
