const express = require('express');
const router = express.Router();
const {
  getAllDepartments,
  getDepartmentById,
  createDepartment,
  updateDepartment,
  deleteDepartment
} = require('./departments.controller');
const { authenticate } = require('../../../middleware/authenticate');
const { authorize } = require('../../../middleware/authorization');

// Apply authentication to all routes
router.use(authenticate);

// GET /api/v1/departments - Get all departments
router.get('/', authorize(['SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN', 'TEACHER']), getAllDepartments);

// Test route to verify routing is working
router.post('/test', (req, res) => {
  console.log('🔍🔍🔍 TEST ROUTE HIT - ROUTING IS WORKING');
  res.json({ success: true, message: 'Test route working', body: req.body });
});

// GET /api/v1/departments/:id - Get department by ID
router.get('/:id', authorize(['SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN', 'TEACHER']), getDepartmentById);

// POST /api/v1/departments - Create new department
router.post('/', (req, res, next) => {
  console.log('🔍🔍🔍 DEPARTMENTS POST ROUTE HIT - THIS SHOULD APPEAR IN LOGS');
  console.log('  - User:', req.user?.id, req.user?.role);
  console.log('  - Body:', req.body);
  console.log('  - Content-Type:', req.headers['content-type']);
  console.log('  - Method:', req.method);
  console.log('  - URL:', req.url);
  next();
}, authorize(['SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN']), createDepartment);

// PUT /api/v1/departments/:id - Update department
router.put('/:id', authorize(['SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN']), updateDepartment);

// DELETE /api/v1/departments/:id - Delete department
router.delete('/:id', authorize(['SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN']), deleteDepartment);

module.exports = router;
