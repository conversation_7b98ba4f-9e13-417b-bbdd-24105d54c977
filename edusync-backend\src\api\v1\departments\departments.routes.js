const express = require('express');
const router = express.Router();
const {
  getAllDepartments,
  getDepartmentById,
  createDepartment,
  updateDepartment,
  deleteDepartment
} = require('./departments.controller');
const { authenticate } = require('../../../middleware/authenticate');
const { authorize } = require('../../../middleware/authorization');

// Apply authentication to all routes
router.use(authenticate);

// GET /api/v1/departments - Get all departments
router.get('/', authorize(['SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN', 'TEACHER']), getAllDepartments);

// GET /api/v1/departments/:id - Get department by ID
router.get('/:id', authorize(['SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN', 'TEACHER']), getDepartmentById);

// POST /api/v1/departments - Create new department
router.post('/', authorize(['SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN']), createDepartment);

// PUT /api/v1/departments/:id - Update department
router.put('/:id', authorize(['SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN']), updateDepartment);

// DELETE /api/v1/departments/:id - Delete department
router.delete('/:id', authorize(['SUPER_ADMIN', 'INSTITUTION_ADMIN', 'SCHOOL_ADMIN']), deleteDepartment);

module.exports = router;
